#!/usr/bin/env python3
"""
MT5 Python Trading Bot - Main Entry Point
Specialized Gold (XAU/USD) Trading Bot with AI/ML Integration
"""

import sys
import signal
import asyncio
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.bot import TradingBot
from src.utils.logger import setup_logger
from src.utils.config import load_config


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print("\nShutdown signal received. Stopping trading bot...")
    sys.exit(0)


async def main():
    """Main entry point for the trading bot"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Setup logging
    logger = setup_logger()
    logger.info("Starting MT5 Gold Trading Bot...")
    
    try:
        # Load configuration
        config = load_config()
        logger.info("Configuration loaded successfully")
        
        # Initialize and start the trading bot
        bot = TradingBot(config)
        await bot.start()
        
    except Exception as e:
        logger.error(f"Failed to start trading bot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
