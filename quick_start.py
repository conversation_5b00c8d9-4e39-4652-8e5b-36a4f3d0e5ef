#!/usr/bin/env python3
"""
Quick Start Script for MT5 Trading Bot
Helps users set up and run the bot quickly
"""

import os
import sys
import subprocess
from pathlib import Path
import yaml

def print_header():
    """Print welcome header"""
    print("=" * 70)
    print("🤖 MT5 PYTHON TRADING BOT - QUICK START")
    print("🥇 Specialized Gold (XAU/USD) Trading with AI/ML")
    print("=" * 70)

def check_python_version():
    """Check Python version"""
    print("\n📋 Checking Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version.split()[0]} - OK")
        return True
    else:
        print(f"❌ Python {sys.version.split()[0]} - Requires Python 3.8+")
        return False

def setup_virtual_environment():
    """Set up virtual environment"""
    print("\n🔧 Setting up virtual environment...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        
        # Activate instructions
        if os.name == 'nt':  # Windows
            activate_cmd = "venv\\Scripts\\activate"
        else:  # Linux/Mac
            activate_cmd = "source venv/bin/activate"
        
        print(f"📝 To activate: {activate_cmd}")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to create virtual environment")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def setup_configuration():
    """Set up configuration file"""
    print("\n⚙️  Setting up configuration...")
    
    config_path = Path("config/config.yaml")
    example_path = Path("config/config.example.yaml")
    
    if config_path.exists():
        print("✅ Configuration file already exists")
        return True
    
    if not example_path.exists():
        print("❌ Example configuration file not found")
        return False
    
    # Create config directory if it doesn't exist
    config_path.parent.mkdir(exist_ok=True)
    
    # Copy example to config
    with open(example_path, 'r') as src, open(config_path, 'w') as dst:
        dst.write(src.read())
    
    print("✅ Configuration file created from example")
    print("📝 Please edit config/config.yaml with your MT5 credentials")
    return True

def validate_mt5_setup():
    """Validate MT5 setup"""
    print("\n🔍 Validating MT5 setup...")
    
    try:
        import MetaTrader5 as mt5
        print("✅ MetaTrader5 package available")
        
        # Try to initialize (will fail if MT5 not installed, but that's OK for now)
        if mt5.initialize():
            print("✅ MT5 terminal connection successful")
            mt5.shutdown()
        else:
            print("⚠️  MT5 terminal not running or not accessible")
            print("   Make sure MT5 terminal is installed and running")
        
        return True
    except ImportError:
        print("❌ MetaTrader5 package not found")
        print("   Try: pip install MetaTrader5")
        return False

def run_tests():
    """Run basic tests"""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test imports
        from src.core.mt5_client import MT5Client
        from src.indicators.technical_indicators import TechnicalIndicators
        from src.models.dqn_agent import DQNAgent
        from src.strategy.gold_strategy import GoldTradingStrategy
        from src.utils.config import load_config
        
        print("✅ All core modules imported successfully")
        
        # Test configuration loading
        try:
            config = load_config("config/config.yaml")
            print("✅ Configuration loaded successfully")
        except Exception as e:
            print(f"⚠️  Configuration loading failed: {e}")
            print("   Please check your config/config.yaml file")
        
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def show_next_steps():
    """Show next steps to user"""
    print("\n🚀 NEXT STEPS:")
    print("-" * 50)
    print("1. 📝 Edit config/config.yaml with your MT5 credentials:")
    print("   - MT5 account login")
    print("   - MT5 password")
    print("   - Broker server name")
    print()
    print("2. 🔧 Make sure MT5 terminal is running")
    print()
    print("3. 🧪 Test the bot in demo mode:")
    print("   python main.py")
    print()
    print("4. 📊 Monitor logs in the logs/ directory")
    print()
    print("5. ⚠️  IMPORTANT: Test thoroughly before live trading!")
    print("   Set live_trading: false in config for demo mode")
    print()
    print("📚 For detailed documentation, see docs/README_DETAILED.md")

def main():
    """Main setup function"""
    print_header()
    
    success_count = 0
    total_steps = 6
    
    # Step 1: Check Python version
    if check_python_version():
        success_count += 1
    
    # Step 2: Setup virtual environment
    if setup_virtual_environment():
        success_count += 1
    
    # Step 3: Install dependencies
    if install_dependencies():
        success_count += 1
    
    # Step 4: Setup configuration
    if setup_configuration():
        success_count += 1
    
    # Step 5: Validate MT5 setup
    if validate_mt5_setup():
        success_count += 1
    
    # Step 6: Run tests
    if run_tests():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 SETUP SUMMARY")
    print("=" * 70)
    print(f"Completed: {success_count}/{total_steps} steps")
    print(f"Success rate: {(success_count/total_steps)*100:.1f}%")
    
    if success_count == total_steps:
        print("🎉 Setup completed successfully!")
        show_next_steps()
    else:
        print("⚠️  Setup incomplete. Please resolve the issues above.")
        print("💡 You can run this script again after fixing the problems.")
    
    print("\n🔗 Support:")
    print("   - GitHub Issues: https://github.com/your-username/mt5-python-trading-bot/issues")
    print("   - Documentation: docs/README_DETAILED.md")
    print("   - Example Config: config/config.example.yaml")

if __name__ == "__main__":
    main()
