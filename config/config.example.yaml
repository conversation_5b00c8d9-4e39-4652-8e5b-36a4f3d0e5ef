# MT5 Python Trading Bot Configuration
# Copy this file to config.yaml and update with your settings

# MetaTrader 5 Connection Settings
mt5:
  login: ********  # Your MT5 account number
  password: "your_password"  # Your MT5 password
  server: "YourBroker-Demo"  # Your broker's server name
  symbol: "XAUUSD"  # Trading symbol (Gold)
  magic_number: 234000  # Unique identifier for bot trades

# Trading Parameters
trading:
  timeframe: 5  # Timeframe in minutes (5 = M5)
  max_positions: 3  # Maximum concurrent positions
  max_daily_trades: 10  # Maximum trades per day
  risk_per_trade: 0.02  # Risk 2% of account per trade
  min_volume: 0.01  # Minimum lot size
  max_volume: 1.0  # Maximum lot size
  volume_step: 0.01  # Volume increment step

# Technical Indicators Settings
indicators:
  macd_fast: 12  # MACD fast EMA period
  macd_slow: 26  # MACD slow EMA period
  macd_signal: 9  # MACD signal line EMA period
  atr_period: 14  # ATR calculation period
  pivot_method: "standard"  # Pivot points method: standard, fibonacci, camarilla

# Risk Management
risk:
  max_drawdown: 0.10  # Maximum 10% drawdown
  stop_loss_atr_multiplier: 2.0  # Stop loss = ATR * 2.0
  take_profit_atr_multiplier: 3.0  # Take profit = ATR * 3.0
  trailing_stop: true  # Enable trailing stop
  trailing_stop_distance: 50  # Trailing stop distance in points
  max_spread: 30  # Maximum allowed spread in points

# AI/ML Model Configuration
ai:
  model_type: "dqn"  # Model type: dqn, ppo, a2c
  learning_rate: 0.001  # Learning rate for neural network
  batch_size: 32  # Training batch size
  memory_size: 10000  # Experience replay memory size
  epsilon_start: 1.0  # Initial exploration rate
  epsilon_end: 0.01  # Final exploration rate
  epsilon_decay: 0.995  # Exploration decay rate
  target_update_frequency: 100  # Target network update frequency
  training_frequency: 4  # Training frequency (every N steps)

# Backtesting Configuration
backtest:
  start_date: "2023-01-01"  # Backtest start date
  end_date: "2024-01-01"  # Backtest end date
  initial_balance: 10000.0  # Starting balance for backtest
  commission: 0.0  # Commission per trade
  spread: 20  # Average spread in points

# General Settings
logging_level: "INFO"  # Logging level: DEBUG, INFO, WARNING, ERROR
enable_ai: true  # Enable AI/ML features
enable_backtesting: false  # Enable backtesting mode
live_trading: false  # Enable live trading (set to true for real trading)

# WARNING: Set live_trading to true only when you're ready for real trading!
# Always test thoroughly in demo mode first.
