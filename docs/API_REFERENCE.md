# MT5 Trading Bot - API Reference

## Core Components

### MT5Client

Main interface for MetaTrader 5 operations.

```python
from src.core.mt5_client import MT5Client

client = MT5Client(login, password, server)
client.connect()
```

#### Methods

- `connect() -> bool`: Connect to MT5 terminal
- `disconnect()`: Disconnect from MT5
- `get_account_info() -> Dict`: Get account information
- `get_symbol_info() -> Dict`: Get symbol information
- `get_current_price() -> Tuple[float, float]`: Get bid/ask prices
- `get_historical_data(timeframe, count) -> pd.DataFrame`: Get historical data
- `buy_market(volume, sl, tp) -> Dict`: Place buy order
- `sell_market(volume, sl, tp) -> Dict`: Place sell order
- `get_positions() -> List[PositionInfo]`: Get open positions
- `close_position(ticket) -> Dict`: Close specific position

### TechnicalIndicators

Calculate technical indicators for market analysis.

```python
from src.indicators.technical_indicators import TechnicalIndicators

indicators = TechnicalIndicators()
macd = indicators.calculate_macd(data)
atr = indicators.calculate_atr(data)
pivots = indicators.calculate_pivot_points(data)
```

#### Methods

- `calculate_macd(data) -> MACDSignal`: Calculate MACD indicator
- `calculate_atr(data) -> ATRData`: Calculate ATR indicator
- `calculate_pivot_points(data) -> PivotPoints`: Calculate pivot points
- `get_combined_signal(data) -> Dict`: Get combined analysis

### RiskManager

Manage trading risk and position sizing.

```python
from src.risk.risk_manager import RiskManager

risk_manager = RiskManager(risk_config, trading_config)
position_size = risk_manager.calculate_position_size(balance, entry, sl, atr, symbol_info)
```

#### Methods

- `calculate_position_size(...) -> PositionSize`: Calculate optimal position size
- `check_trading_allowed(positions, account) -> Tuple[bool, str]`: Check if trading allowed
- `should_close_position(position, price, atr) -> Tuple[bool, str]`: Check if position should close
- `calculate_risk_metrics(account) -> RiskMetrics`: Calculate risk metrics

### GoldTradingStrategy

Main trading strategy for gold.

```python
from src.strategy.gold_strategy import GoldTradingStrategy

strategy = GoldTradingStrategy(config, mt5_client)
market_state = strategy.analyze_market()
signal = strategy.generate_signal(market_state, positions, account)
```

#### Methods

- `analyze_market() -> MarketState`: Analyze current market conditions
- `generate_signal(market_state, positions, account) -> TradingSignal`: Generate trading signal

### DataManager

Manage market data collection and storage.

```python
from src.data.data_manager import DataManager

data_manager = DataManager(mt5_client)
data = data_manager.get_market_data()
```

#### Methods

- `get_market_data(...) -> pd.DataFrame`: Get market data with preprocessing
- `start_real_time_collection()`: Start real-time data collection
- `collect_historical_data(days_back) -> bool`: Collect historical data
- `store_signal(signal_data)`: Store trading signal

### BacktestEngine

Run comprehensive backtests.

```python
from src.backtesting.backtest_engine import BacktestEngine

engine = BacktestEngine(config)
results = engine.run_backtest(data, start_date, end_date)
```

#### Methods

- `run_backtest(data, start_date, end_date) -> BacktestResults`: Run backtest

## Data Structures

### TradingSignal

```python
@dataclass
class TradingSignal:
    action: str          # 'buy', 'sell', 'hold', 'close'
    strength: float      # Signal strength 0-1
    entry_price: float   # Entry price
    stop_loss: float     # Stop loss price
    take_profit: float   # Take profit price
    volume: float        # Position size
    confidence: float    # AI confidence 0-1
    reasoning: str       # Human-readable reasoning
    timestamp: datetime  # Signal timestamp
```

### MarketState

```python
@dataclass
class MarketState:
    price: float              # Current price
    bid: float               # Bid price
    ask: float               # Ask price
    spread: float            # Spread
    macd_signal: MACDSignal  # MACD analysis
    atr_data: ATRData        # ATR analysis
    pivot_points: PivotPoints # Pivot points
    ai_prediction: int       # AI prediction (0=Hold, 1=Buy, 2=Sell)
    ai_confidence: float     # AI confidence
    market_trend: str        # 'bullish', 'bearish', 'sideways'
    volatility: str          # 'low', 'medium', 'high'
```

### PositionSize

```python
@dataclass
class PositionSize:
    volume: float           # Calculated volume
    risk_amount: float      # Risk amount in currency
    stop_loss: float        # Stop loss price
    take_profit: float      # Take profit price
    risk_reward_ratio: float # Risk/reward ratio
```

### BacktestResults

```python
@dataclass
class BacktestResults:
    start_date: datetime
    end_date: datetime
    initial_balance: float
    final_balance: float
    total_return: float
    total_return_pct: float
    max_drawdown: float
    max_drawdown_pct: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    trades: List[Trade]
    equity_curve: pd.Series
    drawdown_curve: pd.Series
```

## Configuration

### BotConfig

Main configuration structure:

```python
@dataclass
class BotConfig:
    mt5: MT5Config              # MT5 connection settings
    trading: TradingConfig      # Trading parameters
    indicators: IndicatorConfig # Indicator settings
    risk: RiskConfig           # Risk management
    ai: AIConfig               # AI/ML settings
    backtest: BacktestConfig   # Backtesting settings
    logging_level: str         # Logging level
    enable_ai: bool            # Enable AI features
    live_trading: bool         # Enable live trading
```

### MT5Config

```python
@dataclass
class MT5Config:
    login: int          # MT5 account number
    password: str       # MT5 password
    server: str         # Broker server
    symbol: str         # Trading symbol (default: XAUUSD)
    magic_number: int   # Magic number for trades
```

### TradingConfig

```python
@dataclass
class TradingConfig:
    timeframe: int          # Timeframe in minutes
    max_positions: int      # Maximum concurrent positions
    max_daily_trades: int   # Maximum trades per day
    risk_per_trade: float   # Risk percentage per trade
    min_volume: float       # Minimum lot size
    max_volume: float       # Maximum lot size
    volume_step: float      # Volume increment
```

### RiskConfig

```python
@dataclass
class RiskConfig:
    max_drawdown: float              # Maximum drawdown (0.1 = 10%)
    stop_loss_atr_multiplier: float  # SL = ATR * multiplier
    take_profit_atr_multiplier: float # TP = ATR * multiplier
    trailing_stop: bool              # Enable trailing stops
    trailing_stop_distance: float    # Trailing distance in points
    max_spread: float                # Maximum spread in points
```

## Usage Examples

### Basic Trading Bot

```python
import asyncio
from src.core.bot import TradingBot
from src.utils.config import load_config

async def main():
    config = load_config()
    bot = TradingBot(config)
    await bot.start()

asyncio.run(main())
```

### Manual Signal Generation

```python
from src.strategy.gold_strategy import GoldTradingStrategy
from src.core.mt5_client import MT5Client

# Setup
client = MT5Client(login, password, server)
client.connect()

strategy = GoldTradingStrategy(config, client)

# Generate signal
market_state = strategy.analyze_market()
positions = client.get_positions()
account = client.get_account_info()

signal = strategy.generate_signal(market_state, positions, account)

if signal and signal.action in ['buy', 'sell']:
    print(f"Signal: {signal.action} at {signal.entry_price}")
    print(f"Strength: {signal.strength:.2f}")
    print(f"Reasoning: {signal.reasoning}")
```

### Running Backtest

```python
from src.backtesting.backtest_engine import BacktestEngine, BacktestReporter
from src.data.data_manager import DataManager

# Setup
data_manager = DataManager(mt5_client)
data = data_manager.get_market_data(count=5000)

# Run backtest
engine = BacktestEngine(config)
results = engine.run_backtest(data)

# Generate report
reporter = BacktestReporter()
report_path = reporter.generate_report(results)
reporter.create_visualizations(results)

print(f"Backtest completed: {results.win_rate:.1%} win rate")
print(f"Report saved: {report_path}")
```

### Custom Indicator Analysis

```python
from src.indicators.technical_indicators import TechnicalIndicators

indicators = TechnicalIndicators()

# Calculate individual indicators
macd = indicators.calculate_macd(data)
print(f"MACD Trend: {macd.trend}")
print(f"MACD Crossover: {macd.crossover}")

atr = indicators.calculate_atr(data)
print(f"ATR: {atr.atr:.2f}")
print(f"Volatility: {atr.volatility_level}")

pivots = indicators.calculate_pivot_points(data)
print(f"Pivot: {pivots.pivot:.2f}")
print(f"Current Level: {pivots.current_level}")

# Combined analysis
combined = indicators.get_combined_signal(data)
print(f"Recommendation: {combined['recommendation']}")
print(f"Signal Strength: {combined['signal_strength']:.2f}")
```

## Error Handling

All components include comprehensive error handling:

```python
try:
    signal = strategy.generate_signal(market_state, positions, account)
except Exception as e:
    logger.error(f"Signal generation failed: {e}")
    # Handle error appropriately
```

## Logging

The bot uses structured logging:

```python
from src.utils.logger import setup_logger, TradingLogger

# General logging
logger = setup_logger()
logger.info("Bot started")

# Trading-specific logging
trading_logger = TradingLogger()
trading_logger.log_trade_entry("XAUUSD", "BUY", 0.1, 2000.0, 1980.0, 2030.0)
```

## Testing

Run tests with pytest:

```bash
# All tests
python -m pytest tests/ -v

# Specific test file
python -m pytest tests/test_indicators.py -v

# Integration tests
python -m pytest tests/test_integration.py -v
```
