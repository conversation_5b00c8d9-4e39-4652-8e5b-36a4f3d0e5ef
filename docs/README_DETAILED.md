# MT5 Python Trading Bot - Detailed Documentation

## Overview

This is an advanced automated trading bot specifically designed for gold (XAU/USD) trading on MetaTrader 5. The bot combines traditional technical analysis with modern AI/ML techniques to make intelligent trading decisions.

## Key Features

### 🎯 Specialized Gold Trading
- Optimized specifically for XAU/USD pair
- 5-minute timeframe focus for scalping strategies
- Real-time market data processing

### 📊 Technical Analysis
- **MACD (Moving Average Convergence Divergence)**: Trend-following momentum indicator
- **ATR (Average True Range)**: Volatility measurement for position sizing
- **Pivot Points**: Support and resistance levels for entry/exit decisions

### 🤖 AI/ML Integration
- **Deep Q-Network (DQN)**: Reinforcement learning for decision making
- **Feature Engineering**: Technical indicator combinations and market state representation
- **Continuous Learning**: Model adapts to changing market conditions

### ⚠️ Risk Management
- Dynamic position sizing based on ATR
- Stop-loss and take-profit automation
- Maximum drawdown protection
- Portfolio risk monitoring
- Trailing stop functionality

## Architecture

### Core Components

1. **MT5 Client** (`src/core/mt5_client.py`)
   - Handles connection to MetaTrader 5
   - Executes trades and manages positions
   - Retrieves market data and account information

2. **Technical Indicators** (`src/indicators/technical_indicators.py`)
   - Calculates MACD, ATR, and Pivot Points
   - Provides combined signal analysis
   - Determines market trend and volatility

3. **AI/ML Models** (`src/models/dqn_agent.py`)
   - Deep Q-Network implementation
   - Experience replay memory
   - Training and inference capabilities

4. **Risk Manager** (`src/risk/risk_manager.py`)
   - Position sizing calculations
   - Risk assessment and monitoring
   - Trade validation and limits

5. **Strategy Engine** (`src/strategy/gold_strategy.py`)
   - Combines technical and AI signals
   - Generates trading decisions
   - Market state analysis

6. **Main Bot** (`src/core/bot.py`)
   - Orchestrates all components
   - Main trading loop
   - Performance monitoring

## Installation

### Prerequisites

1. **Python 3.8+**
2. **MetaTrader 5 Terminal**
3. **MT5 Account with API Access**

### Step-by-Step Installation

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-username/mt5-python-trading-bot.git
   cd mt5-python-trading-bot
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure the Bot**
   ```bash
   cp config/config.example.yaml config/config.yaml
   ```
   Edit `config/config.yaml` with your MT5 credentials and preferences.

## Configuration

### MT5 Settings
```yaml
mt5:
  login: ********          # Your MT5 account number
  password: "your_password" # Your MT5 password
  server: "YourBroker-Demo" # Your broker's server
  symbol: "XAUUSD"         # Trading symbol
  magic_number: 234000     # Unique identifier
```

### Trading Parameters
```yaml
trading:
  timeframe: 5             # 5-minute timeframe
  max_positions: 3         # Maximum concurrent positions
  max_daily_trades: 10     # Daily trade limit
  risk_per_trade: 0.02     # 2% risk per trade
  min_volume: 0.01         # Minimum lot size
  max_volume: 1.0          # Maximum lot size
```

### Risk Management
```yaml
risk:
  max_drawdown: 0.10       # 10% maximum drawdown
  stop_loss_atr_multiplier: 2.0   # SL = ATR * 2.0
  take_profit_atr_multiplier: 3.0 # TP = ATR * 3.0
  trailing_stop: true      # Enable trailing stops
  max_spread: 30           # Maximum spread in points
```

### AI Configuration
```yaml
ai:
  model_type: "dqn"        # Deep Q-Network
  learning_rate: 0.001     # Neural network learning rate
  batch_size: 32           # Training batch size
  memory_size: 10000       # Experience replay memory
  epsilon_start: 1.0       # Initial exploration rate
  epsilon_decay: 0.995     # Exploration decay
```

## Usage

### Running the Bot

1. **Demo Mode (Recommended for Testing)**
   ```bash
   python main.py
   ```
   Set `live_trading: false` in config.yaml

2. **Live Trading Mode**
   ```bash
   python main.py
   ```
   Set `live_trading: true` in config.yaml

### Monitoring

The bot provides comprehensive logging:

- **Console Output**: Real-time status updates
- **Log Files**: Detailed logs in `logs/` directory
- **Trading Log**: Specific trading operations
- **Error Log**: Error tracking and debugging

### Performance Metrics

The bot tracks:
- Total trades executed
- Win rate percentage
- Total profit/loss
- Maximum drawdown
- Sharpe ratio
- Risk-adjusted returns

## Strategy Logic

### Signal Generation

1. **Technical Analysis (70% weight)**
   - MACD crossover signals
   - Trend direction analysis
   - Pivot point levels
   - Volatility assessment

2. **AI Prediction (30% weight)**
   - DQN model prediction
   - Confidence scoring
   - Market state recognition

3. **Combined Decision**
   - Weighted signal strength
   - Minimum threshold filtering
   - Risk validation

### Entry Conditions

**Buy Signal:**
- MACD bullish crossover OR bullish trend
- Price above pivot point
- AI prediction: Buy with high confidence
- Low to medium volatility
- Risk management approval

**Sell Signal:**
- MACD bearish crossover OR bearish trend
- Price below pivot point
- AI prediction: Sell with high confidence
- Low to medium volatility
- Risk management approval

### Exit Conditions

- Stop-loss hit (ATR-based)
- Take-profit reached (ATR-based)
- Trailing stop triggered
- Risk management override
- Maximum position age exceeded

## Risk Management

### Position Sizing
```
Position Size = (Account Balance × Risk%) / (Stop Loss Distance × Point Value)
```

### Risk Controls
- Maximum 3 concurrent positions
- Maximum 10 trades per day
- 10% maximum account drawdown
- Spread filtering (max 30 points)
- Margin level monitoring

### Stop Loss Calculation
```
Stop Loss = Entry Price ± (ATR × 2.0)
```

### Take Profit Calculation
```
Take Profit = Entry Price ± (ATR × 3.0)
```

## AI/ML Components

### Deep Q-Network (DQN)

**Architecture:**
- Input Layer: 20 features (normalized OHLC data)
- Hidden Layers: 3 layers with 128 neurons each
- Output Layer: 3 actions (Hold, Buy, Sell)
- Activation: ReLU with dropout

**Training:**
- Experience Replay Memory
- Target Network Updates
- Epsilon-Greedy Exploration
- Gradient Clipping

**Features:**
- Normalized price data (20 periods)
- Technical indicator values
- Market volatility measures
- Time-based features

## Testing

### Unit Tests
```bash
python -m pytest tests/ -v
```

### Integration Tests
```bash
python -m pytest tests/test_integration.py -v
```

### Backtesting
```bash
python -m pytest tests/test_backtest.py -v
```

## Troubleshooting

### Common Issues

1. **MT5 Connection Failed**
   - Check MT5 terminal is running
   - Verify credentials in config.yaml
   - Ensure API trading is enabled

2. **Symbol Not Found**
   - Verify XAUUSD is available on your broker
   - Check symbol name spelling
   - Ensure market is open

3. **Insufficient Margin**
   - Reduce position size
   - Check account balance
   - Verify leverage settings

4. **High Spread Rejection**
   - Adjust max_spread in config
   - Wait for better market conditions
   - Check broker spread conditions

### Debug Mode

Enable debug logging:
```yaml
logging_level: "DEBUG"
```

### Log Analysis

Check log files in `logs/` directory:
- `mt5_bot_YYYYMMDD.log`: General bot logs
- `trading_YYYYMMDD.log`: Trading operations
- `mt5_bot_errors_YYYYMMDD.log`: Error logs

## Performance Optimization

### Hardware Requirements
- **CPU**: Multi-core processor (4+ cores recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: SSD for faster data access
- **Network**: Stable internet connection

### Software Optimization
- Use virtual environment
- Keep MT5 terminal updated
- Regular log file cleanup
- Monitor system resources

## Security Considerations

### Account Safety
- Use demo account for testing
- Start with small position sizes
- Monitor drawdown closely
- Set appropriate risk limits

### Data Protection
- Secure config.yaml file
- Use environment variables for sensitive data
- Regular backup of configuration
- Monitor unauthorized access

## Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Install development dependencies
4. Run tests before committing
5. Submit pull request

### Code Standards
- Follow PEP 8 style guide
- Add docstrings to functions
- Include unit tests
- Update documentation

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Disclaimer

**IMPORTANT RISK WARNING:**

Trading foreign exchange and CFDs involves significant risk of loss and is not suitable for all investors. Past performance is not indicative of future results. This software is provided for educational and research purposes only. 

- Never trade with money you cannot afford to lose
- Always test thoroughly in demo mode first
- Understand the risks involved in automated trading
- Monitor your account regularly
- Use appropriate risk management

The developers are not responsible for any financial losses incurred through the use of this software.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the troubleshooting section
- Join the community discussions

## Changelog

### Version 1.0.0
- Initial release
- MACD, ATR, Pivot Points indicators
- DQN reinforcement learning
- Comprehensive risk management
- MT5 integration
- Configuration management
- Logging and monitoring
