"""
Backtesting Engine for MT5 Trading Bot
Comprehensive backtesting framework with performance metrics and visualization
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

from ..strategy.gold_strategy import GoldTradingStrategy, TradingSignal, MarketState
from ..indicators.technical_indicators import TechnicalIndicators
from ..risk.risk_manager import RiskManager
from ..utils.logger import get_logger
from ..utils.config import BotConfig, BacktestConfig

logger = get_logger(__name__)


@dataclass
class Trade:
    """Individual trade record"""
    entry_time: datetime
    exit_time: datetime
    symbol: str
    direction: str  # 'long' or 'short'
    entry_price: float
    exit_price: float
    volume: float
    pnl: float
    pnl_pct: float
    commission: float
    swap: float
    duration_minutes: int
    entry_reason: str
    exit_reason: str
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0


@dataclass
class BacktestResults:
    """Backtest results summary"""
    start_date: datetime
    end_date: datetime
    initial_balance: float
    final_balance: float
    total_return: float
    total_return_pct: float
    max_drawdown: float
    max_drawdown_pct: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    avg_trade_duration: float
    trades: List[Trade]
    equity_curve: pd.Series
    drawdown_curve: pd.Series


class BacktestEngine:
    """Main backtesting engine"""

    def __init__(self, config: BotConfig):
        self.config = config
        self.backtest_config = config.backtest

        # Initialize components
        self.indicators = TechnicalIndicators()
        self.risk_manager = RiskManager(config.risk, config.trading)

        # Backtest state
        self.current_time = None
        self.balance = self.backtest_config.initial_balance
        self.equity = self.backtest_config.initial_balance
        self.peak_equity = self.backtest_config.initial_balance
        self.open_positions = []
        self.closed_trades = []
        self.equity_history = []
        self.drawdown_history = []

        # Performance tracking
        self.daily_returns = []
        self.trade_count = 0

    def run_backtest(self, data: pd.DataFrame,
                    start_date: datetime = None,
                    end_date: datetime = None) -> BacktestResults:
        """
        Run comprehensive backtest

        Args:
            data: Historical market data
            start_date: Backtest start date
            end_date: Backtest end date

        Returns:
            BacktestResults object with complete analysis
        """
        logger.info("Starting backtest...")

        # Filter data by date range
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]

        if data.empty:
            raise ValueError("No data available for backtesting")

        # Initialize backtest
        self._initialize_backtest(data.index[0], data.index[-1])

        # Main backtest loop
        for i in range(len(data)):
            current_bar = data.iloc[i:i+1]
            historical_data = data.iloc[:i+1]

            if len(historical_data) < 50:  # Need enough data for indicators
                continue

            self.current_time = current_bar.index[0]
            current_price = current_bar['close'].iloc[0]

            # Update open positions
            self._update_positions(current_bar)

            # Generate trading signal
            signal = self._generate_signal(historical_data, current_bar)

            # Execute signal
            if signal:
                self._execute_signal(signal, current_bar)

            # Update equity and drawdown
            self._update_equity(current_price)

            # Log progress
            if i % 1000 == 0:
                logger.info(f"Processed {i}/{len(data)} bars ({i/len(data)*100:.1f}%)")

        # Close remaining positions
        self._close_all_positions(data.iloc[-1])

        # Calculate final results
        results = self._calculate_results()

        logger.info(f"Backtest completed: {results.total_trades} trades, "
                   f"{results.win_rate:.1f}% win rate, "
                   f"{results.total_return_pct:.2f}% return")

        return results

    def _initialize_backtest(self, start_date: datetime, end_date: datetime):
        """Initialize backtest state"""
        self.balance = self.backtest_config.initial_balance
        self.equity = self.backtest_config.initial_balance
        self.peak_equity = self.backtest_config.initial_balance
        self.open_positions = []
        self.closed_trades = []
        self.equity_history = []
        self.drawdown_history = []
        self.daily_returns = []
        self.trade_count = 0

        logger.info(f"Backtest initialized: {start_date} to {end_date}")
        logger.info(f"Initial balance: ${self.balance:.2f}")

    def _generate_signal(self, historical_data: pd.DataFrame,
                        current_bar: pd.DataFrame) -> Optional[TradingSignal]:
        """Generate trading signal for current bar"""
        try:
            # Calculate indicators
            macd_signal = self.indicators.calculate_macd(historical_data)
            atr_data = self.indicators.calculate_atr(historical_data)
            pivot_points = self.indicators.calculate_pivot_points(historical_data)

            # Create market state
            current_price = current_bar['close'].iloc[0]
            market_state = MarketState(
                price=current_price,
                bid=current_price - 0.0001,  # Simulate spread
                ask=current_price + 0.0001,
                spread=0.0002,
                macd_signal=macd_signal,
                atr_data=atr_data,
                pivot_points=pivot_points,
                ai_prediction=None,
                ai_confidence=0.0,
                market_trend=self._determine_trend(historical_data),
                volatility=atr_data.volatility_level
            )

            # Generate signal using strategy logic
            signal = self._evaluate_strategy_conditions(market_state)

            return signal

        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return None

    def _evaluate_strategy_conditions(self, market_state: MarketState) -> Optional[TradingSignal]:
        """Evaluate strategy conditions for signal generation"""
        # Check if we can open new positions
        if len(self.open_positions) >= self.config.trading.max_positions:
            return None

        # Calculate technical score
        buy_score = 0
        sell_score = 0
        factors = []

        # MACD analysis
        if market_state.macd_signal.crossover == 'bullish_cross':
            buy_score += 0.4
            factors.append('MACD bullish crossover')
        elif market_state.macd_signal.crossover == 'bearish_cross':
            sell_score += 0.4
            factors.append('MACD bearish crossover')
        elif market_state.macd_signal.trend == 'bullish':
            buy_score += 0.2
            factors.append('MACD bullish trend')
        elif market_state.macd_signal.trend == 'bearish':
            sell_score += 0.2
            factors.append('MACD bearish trend')

        # Pivot Points analysis
        if market_state.pivot_points.current_level == 'above_pivot':
            buy_score += 0.2
            factors.append('Price above pivot')
        elif market_state.pivot_points.current_level == 'below_pivot':
            sell_score += 0.2
            factors.append('Price below pivot')

        # Market trend
        if market_state.market_trend == 'bullish':
            buy_score += 0.2
            factors.append('Bullish trend')
        elif market_state.market_trend == 'bearish':
            sell_score += 0.2
            factors.append('Bearish trend')

        # Generate signal if score is high enough
        min_score = 0.6

        if buy_score > min_score:
            return self._create_buy_signal(market_state, buy_score, factors)
        elif sell_score > min_score:
            return self._create_sell_signal(market_state, sell_score, factors)

        return None

    def _create_buy_signal(self, market_state: MarketState,
                          score: float, factors: List[str]) -> TradingSignal:
        """Create buy signal"""
        entry_price = market_state.ask
        atr = market_state.atr_data.atr

        stop_loss = entry_price - (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price + (atr * self.config.risk.take_profit_atr_multiplier)

        # Calculate position size
        risk_amount = self.balance * self.config.trading.risk_per_trade
        sl_distance = entry_price - stop_loss
        volume = min(risk_amount / (sl_distance * 100), self.config.trading.max_volume)
        volume = max(volume, self.config.trading.min_volume)

        return TradingSignal(
            action='buy',
            strength=score,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=volume,
            confidence=score,
            reasoning="BUY: " + " | ".join(factors),
            timestamp=self.current_time
        )

    def _create_sell_signal(self, market_state: MarketState,
                           score: float, factors: List[str]) -> TradingSignal:
        """Create sell signal"""
        entry_price = market_state.bid
        atr = market_state.atr_data.atr

        stop_loss = entry_price + (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price - (atr * self.config.risk.take_profit_atr_multiplier)

        # Calculate position size
        risk_amount = self.balance * self.config.trading.risk_per_trade
        sl_distance = stop_loss - entry_price
        volume = min(risk_amount / (sl_distance * 100), self.config.trading.max_volume)
        volume = max(volume, self.config.trading.min_volume)

        return TradingSignal(
            action='sell',
            strength=score,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=volume,
            confidence=score,
            reasoning="SELL: " + " | ".join(factors),
            timestamp=self.current_time
        )

    def _execute_signal(self, signal: TradingSignal, current_bar: pd.DataFrame):
        """Execute trading signal"""
        if signal.action in ['buy', 'sell']:
            # Create new position
            position = {
                'entry_time': self.current_time,
                'symbol': 'XAUUSD',
                'direction': 'long' if signal.action == 'buy' else 'short',
                'entry_price': signal.entry_price,
                'volume': signal.volume,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'entry_reason': signal.reasoning,
                'max_favorable': 0.0,
                'max_adverse': 0.0
            }

            self.open_positions.append(position)
            self.trade_count += 1

            # Deduct commission
            commission = self.backtest_config.commission
            self.balance -= commission

    def _update_positions(self, current_bar: pd.DataFrame):
        """Update open positions and check for exits"""
        current_price = current_bar['close'].iloc[0]
        high_price = current_bar['high'].iloc[0]
        low_price = current_bar['low'].iloc[0]

        positions_to_close = []

        for i, position in enumerate(self.open_positions):
            # Update max favorable/adverse excursion
            if position['direction'] == 'long':
                favorable = high_price - position['entry_price']
                adverse = position['entry_price'] - low_price

                # Check stop loss and take profit
                if low_price <= position['stop_loss']:
                    positions_to_close.append((i, position['stop_loss'], 'Stop Loss'))
                elif high_price >= position['take_profit']:
                    positions_to_close.append((i, position['take_profit'], 'Take Profit'))
            else:  # short
                favorable = position['entry_price'] - low_price
                adverse = high_price - position['entry_price']

                # Check stop loss and take profit
                if high_price >= position['stop_loss']:
                    positions_to_close.append((i, position['stop_loss'], 'Stop Loss'))
                elif low_price <= position['take_profit']:
                    positions_to_close.append((i, position['take_profit'], 'Take Profit'))

            # Update excursions
            position['max_favorable'] = max(position['max_favorable'], favorable)
            position['max_adverse'] = max(position['max_adverse'], adverse)

        # Close positions
        for i, exit_price, exit_reason in reversed(positions_to_close):
            self._close_position(i, exit_price, exit_reason)

    def _close_position(self, position_index: int, exit_price: float, exit_reason: str):
        """Close a specific position"""
        position = self.open_positions[position_index]

        # Calculate P&L
        if position['direction'] == 'long':
            pnl = (exit_price - position['entry_price']) * position['volume'] * 100
        else:
            pnl = (position['entry_price'] - exit_price) * position['volume'] * 100

        pnl_pct = pnl / (position['entry_price'] * position['volume'] * 100)

        # Create trade record
        duration = (self.current_time - position['entry_time']).total_seconds() / 60

        trade = Trade(
            entry_time=position['entry_time'],
            exit_time=self.current_time,
            symbol=position['symbol'],
            direction=position['direction'],
            entry_price=position['entry_price'],
            exit_price=exit_price,
            volume=position['volume'],
            pnl=pnl,
            pnl_pct=pnl_pct,
            commission=self.backtest_config.commission * 2,  # Entry + Exit
            swap=0.0,
            duration_minutes=int(duration),
            entry_reason=position['entry_reason'],
            exit_reason=exit_reason,
            max_favorable_excursion=position['max_favorable'],
            max_adverse_excursion=position['max_adverse']
        )

        self.closed_trades.append(trade)

        # Update balance
        self.balance += pnl - trade.commission

        # Remove position
        del self.open_positions[position_index]

    def _close_all_positions(self, final_bar: pd.DataFrame):
        """Close all remaining positions at the end of backtest"""
        final_price = final_bar['close'].iloc[0]

        while self.open_positions:
            self._close_position(0, final_price, 'End of Backtest')

    def _update_equity(self, current_price: float):
        """Update equity and drawdown calculations"""
        # Calculate unrealized P&L
        unrealized_pnl = 0
        for position in self.open_positions:
            if position['direction'] == 'long':
                unrealized_pnl += (current_price - position['entry_price']) * position['volume'] * 100
            else:
                unrealized_pnl += (position['entry_price'] - current_price) * position['volume'] * 100

        self.equity = self.balance + unrealized_pnl

        # Update peak equity and drawdown
        if self.equity > self.peak_equity:
            self.peak_equity = self.equity

        drawdown = (self.peak_equity - self.equity) / self.peak_equity

        # Store history
        self.equity_history.append({
            'time': self.current_time,
            'equity': self.equity,
            'balance': self.balance,
            'drawdown': drawdown
        })

    def _determine_trend(self, data: pd.DataFrame) -> str:
        """Determine market trend from price data"""
        if len(data) < 20:
            return 'sideways'

        recent_prices = data['close'].tail(20)
        if recent_prices.iloc[-1] > recent_prices.iloc[0]:
            return 'bullish'
        elif recent_prices.iloc[-1] < recent_prices.iloc[0]:
            return 'bearish'
        else:
            return 'sideways'

    def _calculate_results(self) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        if not self.closed_trades:
            raise ValueError("No trades executed during backtest")

        # Basic metrics
        total_trades = len(self.closed_trades)
        winning_trades = sum(1 for trade in self.closed_trades if trade.pnl > 0)
        losing_trades = total_trades - winning_trades

        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # P&L metrics
        total_pnl = sum(trade.pnl for trade in self.closed_trades)
        total_return_pct = (self.balance - self.backtest_config.initial_balance) / self.backtest_config.initial_balance

        # Win/Loss analysis
        winning_pnls = [trade.pnl for trade in self.closed_trades if trade.pnl > 0]
        losing_pnls = [trade.pnl for trade in self.closed_trades if trade.pnl < 0]

        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0

        profit_factor = abs(sum(winning_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')

        # Drawdown analysis
        equity_series = pd.Series([h['equity'] for h in self.equity_history])
        drawdown_series = pd.Series([h['drawdown'] for h in self.equity_history])

        max_drawdown = drawdown_series.max()
        max_drawdown_pct = max_drawdown

        # Risk metrics
        returns = equity_series.pct_change().dropna()
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = total_return_pct / max_drawdown_pct if max_drawdown_pct > 0 else 0

        # Consecutive wins/losses
        max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_trades()

        # Average trade duration
        avg_duration = np.mean([trade.duration_minutes for trade in self.closed_trades])

        return BacktestResults(
            start_date=self.equity_history[0]['time'] if self.equity_history else datetime.now(),
            end_date=self.equity_history[-1]['time'] if self.equity_history else datetime.now(),
            initial_balance=self.backtest_config.initial_balance,
            final_balance=self.balance,
            total_return=total_pnl,
            total_return_pct=total_return_pct,
            max_drawdown=max_drawdown * self.backtest_config.initial_balance,
            max_drawdown_pct=max_drawdown_pct,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            avg_trade_duration=avg_duration,
            trades=self.closed_trades,
            equity_curve=equity_series,
            drawdown_curve=drawdown_series
        )

    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """Calculate Sharpe ratio"""
        if returns.std() == 0:
            return 0
        return returns.mean() / returns.std() * np.sqrt(252 * 24 * 12)  # Annualized for 5-min data

    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """Calculate Sortino ratio"""
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0 or negative_returns.std() == 0:
            return 0
        return returns.mean() / negative_returns.std() * np.sqrt(252 * 24 * 12)

    def _calculate_consecutive_trades(self) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses"""
        if not self.closed_trades:
            return 0, 0

        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0

        for trade in self.closed_trades:
            if trade.pnl > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)

        return max_wins, max_losses


class BacktestReporter:
    """Generate backtest reports and visualizations"""

    def __init__(self):
        self.report_dir = Path("reports")
        self.report_dir.mkdir(exist_ok=True)

    def generate_report(self, results: BacktestResults, save_path: str = None) -> str:
        """Generate comprehensive backtest report"""
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = self.report_dir / f"backtest_report_{timestamp}.html"

        # Create HTML report
        html_content = self._create_html_report(results)

        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Backtest report saved to {save_path}")
        return str(save_path)

    def _create_html_report(self, results: BacktestResults) -> str:
        """Create HTML report content"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>MT5 Gold Trading Bot - Backtest Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-card {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
                .metric-label {{ font-size: 14px; color: #666; }}
                .positive {{ color: #28a745; }}
                .negative {{ color: #dc3545; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f0f0f0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 MT5 Gold Trading Bot - Backtest Report</h1>
                <p><strong>Period:</strong> {results.start_date.strftime('%Y-%m-%d')} to {results.end_date.strftime('%Y-%m-%d')}</p>
                <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <h2>📊 Performance Summary</h2>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value {'positive' if results.total_return_pct > 0 else 'negative'}">
                        {results.total_return_pct:.2%}
                    </div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.total_trades}</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value {'positive' if results.win_rate > 0.5 else 'negative'}">
                        {results.win_rate:.1%}
                    </div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value negative">{results.max_drawdown_pct:.2%}</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.profit_factor:.2f}</div>
                    <div class="metric-label">Profit Factor</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.sharpe_ratio:.2f}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
            </div>

            <h2>💰 Financial Metrics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Initial Balance</td><td>${results.initial_balance:,.2f}</td></tr>
                <tr><td>Final Balance</td><td>${results.final_balance:,.2f}</td></tr>
                <tr><td>Total Return</td><td>${results.total_return:,.2f}</td></tr>
                <tr><td>Average Win</td><td>${results.avg_win:,.2f}</td></tr>
                <tr><td>Average Loss</td><td>${results.avg_loss:,.2f}</td></tr>
                <tr><td>Max Drawdown</td><td>${results.max_drawdown:,.2f}</td></tr>
            </table>

            <h2>📈 Trading Statistics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Winning Trades</td><td>{results.winning_trades}</td></tr>
                <tr><td>Losing Trades</td><td>{results.losing_trades}</td></tr>
                <tr><td>Max Consecutive Wins</td><td>{results.max_consecutive_wins}</td></tr>
                <tr><td>Max Consecutive Losses</td><td>{results.max_consecutive_losses}</td></tr>
                <tr><td>Average Trade Duration</td><td>{results.avg_trade_duration:.1f} minutes</td></tr>
                <tr><td>Sortino Ratio</td><td>{results.sortino_ratio:.2f}</td></tr>
                <tr><td>Calmar Ratio</td><td>{results.calmar_ratio:.2f}</td></tr>
            </table>

            <h2>📋 Recent Trades</h2>
            <table>
                <tr>
                    <th>Entry Time</th>
                    <th>Direction</th>
                    <th>Entry Price</th>
                    <th>Exit Price</th>
                    <th>P&L</th>
                    <th>Duration</th>
                </tr>
                {self._generate_trades_table(results.trades[-20:])}
            </table>

            <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
                <p><strong>Disclaimer:</strong> Past performance is not indicative of future results.
                Trading involves significant risk of loss.</p>
            </footer>
        </body>
        </html>
        """

    def _generate_trades_table(self, trades: List[Trade]) -> str:
        """Generate HTML table rows for trades"""
        rows = []
        for trade in trades:
            pnl_class = 'positive' if trade.pnl > 0 else 'negative'
            rows.append(f"""
                <tr>
                    <td>{trade.entry_time.strftime('%Y-%m-%d %H:%M')}</td>
                    <td>{trade.direction.upper()}</td>
                    <td>{trade.entry_price:.5f}</td>
                    <td>{trade.exit_price:.5f}</td>
                    <td class="{pnl_class}">${trade.pnl:.2f}</td>
                    <td>{trade.duration_minutes} min</td>
                </tr>
            """)
        return ''.join(rows)

    def create_visualizations(self, results: BacktestResults, save_dir: str = None):
        """Create backtest visualization charts"""
        if save_dir is None:
            save_dir = self.report_dir
        else:
            save_dir = Path(save_dir)
            save_dir.mkdir(exist_ok=True)

        # Set style
        plt.style.use('seaborn-v0_8')

        # 1. Equity Curve
        self._plot_equity_curve(results, save_dir)

        # 2. Drawdown Chart
        self._plot_drawdown(results, save_dir)

        # 3. Monthly Returns Heatmap
        self._plot_monthly_returns(results, save_dir)

        # 4. Trade Analysis
        self._plot_trade_analysis(results, save_dir)

        logger.info(f"Visualizations saved to {save_dir}")

    def _plot_equity_curve(self, results: BacktestResults, save_dir: Path):
        """Plot equity curve"""
        fig, ax = plt.subplots(figsize=(12, 6))

        ax.plot(results.equity_curve.index, results.equity_curve.values,
                linewidth=2, color='blue', label='Equity')
        ax.axhline(y=results.initial_balance, color='gray', linestyle='--',
                  alpha=0.7, label='Initial Balance')

        ax.set_title('Equity Curve', fontsize=16, fontweight='bold')
        ax.set_xlabel('Time')
        ax.set_ylabel('Equity ($)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_dir / 'equity_curve.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_drawdown(self, results: BacktestResults, save_dir: Path):
        """Plot drawdown chart"""
        fig, ax = plt.subplots(figsize=(12, 6))

        ax.fill_between(results.drawdown_curve.index,
                       results.drawdown_curve.values * 100, 0,
                       color='red', alpha=0.3, label='Drawdown')
        ax.plot(results.drawdown_curve.index,
               results.drawdown_curve.values * 100,
               color='red', linewidth=1)

        ax.set_title('Drawdown Chart', fontsize=16, fontweight='bold')
        ax.set_xlabel('Time')
        ax.set_ylabel('Drawdown (%)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_dir / 'drawdown.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_monthly_returns(self, results: BacktestResults, save_dir: Path):
        """Plot monthly returns heatmap"""
        # Calculate monthly returns
        monthly_returns = results.equity_curve.resample('M').last().pct_change().dropna()

        if len(monthly_returns) < 2:
            return  # Not enough data for monthly analysis

        # Create pivot table for heatmap
        monthly_data = pd.DataFrame({
            'Year': monthly_returns.index.year,
            'Month': monthly_returns.index.month,
            'Return': monthly_returns.values * 100
        })

        pivot_table = monthly_data.pivot(index='Year', columns='Month', values='Return')

        fig, ax = plt.subplots(figsize=(12, 8))
        sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlGn',
                   center=0, ax=ax, cbar_kws={'label': 'Return (%)'})

        ax.set_title('Monthly Returns Heatmap', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Year')

        plt.tight_layout()
        plt.savefig(save_dir / 'monthly_returns.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_trade_analysis(self, results: BacktestResults, save_dir: Path):
        """Plot trade analysis charts"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 1. P&L Distribution
        pnls = [trade.pnl for trade in results.trades]
        ax1.hist(pnls, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title('P&L Distribution')
        ax1.set_xlabel('P&L ($)')
        ax1.set_ylabel('Frequency')

        # 2. Trade Duration Distribution
        durations = [trade.duration_minutes for trade in results.trades]
        ax2.hist(durations, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax2.set_title('Trade Duration Distribution')
        ax2.set_xlabel('Duration (minutes)')
        ax2.set_ylabel('Frequency')

        # 3. Cumulative P&L
        cumulative_pnl = np.cumsum(pnls)
        ax3.plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2, color='purple')
        ax3.set_title('Cumulative P&L by Trade')
        ax3.set_xlabel('Trade Number')
        ax3.set_ylabel('Cumulative P&L ($)')
        ax3.grid(True, alpha=0.3)

        # 4. Win/Loss Ratio by Month
        trade_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])
        trade_df['month'] = trade_df['entry_time'].dt.to_period('M')

        monthly_stats = trade_df.groupby('month').agg({
            'pnl': ['count', lambda x: (x > 0).sum(), lambda x: (x < 0).sum()]
        }).round(2)

        if len(monthly_stats) > 0:
            monthly_stats.columns = ['Total', 'Wins', 'Losses']
            monthly_stats['Win_Rate'] = monthly_stats['Wins'] / monthly_stats['Total'] * 100

            ax4.bar(range(len(monthly_stats)), monthly_stats['Win_Rate'],
                   alpha=0.7, color='orange', edgecolor='black')
            ax4.axhline(y=50, color='red', linestyle='--', alpha=0.7)
            ax4.set_title('Monthly Win Rate')
            ax4.set_xlabel('Month')
            ax4.set_ylabel('Win Rate (%)')
            ax4.set_xticks(range(len(monthly_stats)))
            ax4.set_xticklabels([str(m) for m in monthly_stats.index], rotation=45)

        plt.tight_layout()
        plt.savefig(save_dir / 'trade_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
