"""
Gold Trading Strategy Engine
Combines MACD, ATR, Pivot Points with AI/ML predictions for trading decisions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import MetaTrader5 as mt5

from ..indicators.technical_indicators import TechnicalIndicators, MACDSignal, ATRData, PivotPoints
from ..models.dqn_agent import DQNAgent
from ..risk.risk_manager import RiskManager, PositionSize
from ..core.mt5_client import MT5Client, PositionInfo
from ..utils.logger import get_logger, TradingLogger
from ..utils.config import BotConfig

logger = get_logger(__name__)
trading_logger = TradingLogger("strategy")


@dataclass
class TradingSignal:
    """Trading signal data structure"""
    action: str  # 'buy', 'sell', 'hold', 'close'
    strength: float  # Signal strength 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    volume: float
    confidence: float  # AI confidence 0-1
    reasoning: str  # Human-readable reasoning
    timestamp: datetime


@dataclass
class MarketState:
    """Current market state representation"""
    price: float
    bid: float
    ask: float
    spread: float
    macd_signal: MACDSignal
    atr_data: ATRData
    pivot_points: PivotPoints
    ai_prediction: Optional[int]
    ai_confidence: float
    market_trend: str  # 'bullish', 'bearish', 'sideways'
    volatility: str  # 'low', 'medium', 'high'


class GoldTradingStrategy:
    """Advanced Gold Trading Strategy with AI Integration"""
    
    def __init__(self, config: BotConfig, mt5_client: MT5Client):
        self.config = config
        self.mt5_client = mt5_client
        
        # Initialize components
        self.indicators = TechnicalIndicators()
        self.risk_manager = RiskManager(config.risk, config.trading)
        
        # Initialize AI agent if enabled
        self.ai_agent = None
        if config.enable_ai:
            state_size = 20  # Feature vector size
            action_size = 3  # Hold, Buy, Sell
            self.ai_agent = DQNAgent(state_size, action_size, config.ai)
            self._load_ai_model()
        
        # Strategy parameters
        self.min_signal_strength = 0.6
        self.min_ai_confidence = 0.7
        self.lookback_period = 100
        
        # State tracking
        self.last_signal = None
        self.market_data_cache = None
        self.feature_cache = None
        
        logger.info("Gold Trading Strategy initialized")
    
    def analyze_market(self, timeframe: int = mt5.TIMEFRAME_M5) -> MarketState:
        """
        Analyze current market conditions
        
        Args:
            timeframe: MT5 timeframe constant
        
        Returns:
            MarketState object with current analysis
        """
        # Get market data
        data = self.mt5_client.get_historical_data(timeframe, self.lookback_period)
        if data is None or len(data) < 50:
            logger.error("Insufficient market data for analysis")
            return self._create_empty_market_state()
        
        self.market_data_cache = data
        
        # Get current prices
        bid, ask = self.mt5_client.get_current_price()
        if bid is None or ask is None:
            logger.error("Failed to get current prices")
            return self._create_empty_market_state()
        
        current_price = (bid + ask) / 2
        spread = ask - bid
        
        # Calculate technical indicators
        macd_signal = self.indicators.calculate_macd(data)
        atr_data = self.indicators.calculate_atr(data)
        pivot_points = self.indicators.calculate_pivot_points(data)
        
        # Get AI prediction if enabled
        ai_prediction = None
        ai_confidence = 0.0
        if self.ai_agent and self.config.enable_ai:
            features = self._extract_features(data)
            if features is not None:
                ai_prediction = self.ai_agent.get_action(features, training=False)
                q_values = self.ai_agent.get_q_values(features)
                ai_confidence = np.max(q_values) / (np.sum(np.abs(q_values)) + 1e-8)
        
        # Determine market trend and volatility
        market_trend = self._determine_market_trend(data, macd_signal, pivot_points)
        volatility = atr_data.volatility_level
        
        return MarketState(
            price=current_price,
            bid=bid,
            ask=ask,
            spread=spread,
            macd_signal=macd_signal,
            atr_data=atr_data,
            pivot_points=pivot_points,
            ai_prediction=ai_prediction,
            ai_confidence=ai_confidence,
            market_trend=market_trend,
            volatility=volatility
        )
    
    def generate_signal(self, market_state: MarketState, 
                       current_positions: List[PositionInfo],
                       account_info: Dict) -> Optional[TradingSignal]:
        """
        Generate trading signal based on market analysis
        
        Args:
            market_state: Current market state
            current_positions: List of open positions
            account_info: Account information
        
        Returns:
            TradingSignal if signal generated, None otherwise
        """
        # Check if trading is allowed
        trading_allowed, reason = self.risk_manager.check_trading_allowed(
            current_positions, account_info
        )
        
        if not trading_allowed:
            logger.info(f"Trading not allowed: {reason}")
            return None
        
        # Check spread conditions
        symbol_info = self.mt5_client.get_symbol_info()
        if not self.risk_manager.check_spread_condition(symbol_info):
            logger.info(f"Spread too high: {market_state.spread}")
            return None
        
        # Generate signal based on strategy rules
        signal = self._evaluate_trading_conditions(market_state, current_positions)
        
        if signal is None:
            return None
        
        # Calculate position sizing
        position_size = self.risk_manager.calculate_position_size(
            account_info['balance'],
            signal.entry_price,
            signal.stop_loss,
            market_state.atr_data.atr,
            symbol_info
        )
        
        # Update signal with calculated volume
        signal.volume = position_size.volume
        signal.stop_loss = position_size.stop_loss
        signal.take_profit = position_size.take_profit
        
        # Log signal generation
        trading_logger.log_signal(
            signal.action,
            signal.strength,
            {
                'macd_trend': market_state.macd_signal.trend,
                'pivot_level': market_state.pivot_points.current_level,
                'ai_prediction': market_state.ai_prediction,
                'ai_confidence': market_state.ai_confidence
            },
            reasoning=signal.reasoning
        )
        
        self.last_signal = signal
        return signal
    
    def _evaluate_trading_conditions(self, market_state: MarketState,
                                   current_positions: List[PositionInfo]) -> Optional[TradingSignal]:
        """Evaluate trading conditions and generate signal"""
        
        # Check for position management first
        if current_positions:
            close_signal = self._check_position_management(market_state, current_positions)
            if close_signal:
                return close_signal
        
        # Don't open new positions if we already have maximum positions
        if len(current_positions) >= self.config.trading.max_positions:
            return None
        
        # Calculate signal components
        technical_score = self._calculate_technical_score(market_state)
        ai_score = self._calculate_ai_score(market_state)
        
        # Combine scores
        combined_score = self._combine_scores(technical_score, ai_score, market_state)
        
        # Generate signal based on combined score
        if combined_score['buy_strength'] > self.min_signal_strength:
            return self._create_buy_signal(market_state, combined_score)
        elif combined_score['sell_strength'] > self.min_signal_strength:
            return self._create_sell_signal(market_state, combined_score)
        
        return None
    
    def _calculate_technical_score(self, market_state: MarketState) -> Dict:
        """Calculate technical analysis score"""
        score = {'buy': 0, 'sell': 0, 'factors': []}
        
        # MACD analysis
        if market_state.macd_signal.crossover == 'bullish_cross':
            score['buy'] += 0.4
            score['factors'].append('MACD bullish crossover')
        elif market_state.macd_signal.crossover == 'bearish_cross':
            score['sell'] += 0.4
            score['factors'].append('MACD bearish crossover')
        elif market_state.macd_signal.trend == 'bullish':
            score['buy'] += 0.2
            score['factors'].append('MACD bullish trend')
        elif market_state.macd_signal.trend == 'bearish':
            score['sell'] += 0.2
            score['factors'].append('MACD bearish trend')
        
        # Pivot Points analysis
        if market_state.pivot_points.current_level == 'above_pivot':
            score['buy'] += 0.2
            score['factors'].append('Price above pivot')
        elif market_state.pivot_points.current_level == 'below_pivot':
            score['sell'] += 0.2
            score['factors'].append('Price below pivot')
        
        # Market trend analysis
        if market_state.market_trend == 'bullish':
            score['buy'] += 0.2
            score['factors'].append('Bullish market trend')
        elif market_state.market_trend == 'bearish':
            score['sell'] += 0.2
            score['factors'].append('Bearish market trend')
        
        # Volatility filter
        if market_state.volatility == 'high':
            score['buy'] *= 0.7  # Reduce signal strength in high volatility
            score['sell'] *= 0.7
            score['factors'].append('High volatility - reduced confidence')
        
        return score
    
    def _calculate_ai_score(self, market_state: MarketState) -> Dict:
        """Calculate AI model score"""
        score = {'buy': 0, 'sell': 0, 'factors': []}
        
        if not self.config.enable_ai or market_state.ai_prediction is None:
            return score
        
        # AI prediction: 0=Hold, 1=Buy, 2=Sell
        if market_state.ai_prediction == 1 and market_state.ai_confidence > self.min_ai_confidence:
            score['buy'] += 0.5 * market_state.ai_confidence
            score['factors'].append(f'AI buy prediction (confidence: {market_state.ai_confidence:.2f})')
        elif market_state.ai_prediction == 2 and market_state.ai_confidence > self.min_ai_confidence:
            score['sell'] += 0.5 * market_state.ai_confidence
            score['factors'].append(f'AI sell prediction (confidence: {market_state.ai_confidence:.2f})')
        
        return score
    
    def _combine_scores(self, technical_score: Dict, ai_score: Dict, 
                       market_state: MarketState) -> Dict:
        """Combine technical and AI scores"""
        # Weight factors
        technical_weight = 0.7 if self.config.enable_ai else 1.0
        ai_weight = 0.3 if self.config.enable_ai else 0.0
        
        buy_strength = (technical_score['buy'] * technical_weight + 
                       ai_score['buy'] * ai_weight)
        sell_strength = (technical_score['sell'] * technical_weight + 
                        ai_score['sell'] * ai_weight)
        
        # Combine factors
        all_factors = technical_score['factors'] + ai_score['factors']
        
        return {
            'buy_strength': buy_strength,
            'sell_strength': sell_strength,
            'factors': all_factors
        }
    
    def _create_buy_signal(self, market_state: MarketState, 
                          combined_score: Dict) -> TradingSignal:
        """Create buy signal"""
        entry_price = market_state.ask
        atr = market_state.atr_data.atr
        
        # Calculate stop loss and take profit
        stop_loss = entry_price - (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price + (atr * self.config.risk.take_profit_atr_multiplier)
        
        reasoning = "BUY: " + " | ".join(combined_score['factors'])
        
        return TradingSignal(
            action='buy',
            strength=combined_score['buy_strength'],
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=0.01,  # Will be updated by risk manager
            confidence=market_state.ai_confidence,
            reasoning=reasoning,
            timestamp=datetime.now()
        )
    
    def _create_sell_signal(self, market_state: MarketState, 
                           combined_score: Dict) -> TradingSignal:
        """Create sell signal"""
        entry_price = market_state.bid
        atr = market_state.atr_data.atr
        
        # Calculate stop loss and take profit
        stop_loss = entry_price + (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price - (atr * self.config.risk.take_profit_atr_multiplier)
        
        reasoning = "SELL: " + " | ".join(combined_score['factors'])
        
        return TradingSignal(
            action='sell',
            strength=combined_score['sell_strength'],
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=0.01,  # Will be updated by risk manager
            confidence=market_state.ai_confidence,
            reasoning=reasoning,
            timestamp=datetime.now()
        )
    
    def _check_position_management(self, market_state: MarketState,
                                 positions: List[PositionInfo]) -> Optional[TradingSignal]:
        """Check if any positions should be closed"""
        for position in positions:
            should_close, reason = self.risk_manager.should_close_position(
                position, market_state.price, market_state.atr_data.atr
            )
            
            if should_close:
                return TradingSignal(
                    action='close',
                    strength=1.0,
                    entry_price=market_state.price,
                    stop_loss=0,
                    take_profit=0,
                    volume=position.volume,
                    confidence=1.0,
                    reasoning=f"Close position {position.ticket}: {reason}",
                    timestamp=datetime.now()
                )
        
        return None
    
    def _determine_market_trend(self, data: pd.DataFrame, 
                              macd_signal: MACDSignal,
                              pivot_points: PivotPoints) -> str:
        """Determine overall market trend"""
        # Simple trend determination based on price action and indicators
        recent_prices = data['close'].tail(20)
        price_trend = 'bullish' if recent_prices.iloc[-1] > recent_prices.iloc[0] else 'bearish'
        
        # Combine with MACD and pivot analysis
        if (macd_signal.trend == 'bullish' and 
            pivot_points.current_level == 'above_pivot' and 
            price_trend == 'bullish'):
            return 'bullish'
        elif (macd_signal.trend == 'bearish' and 
              pivot_points.current_level == 'below_pivot' and 
              price_trend == 'bearish'):
            return 'bearish'
        else:
            return 'sideways'
    
    def _extract_features(self, data: pd.DataFrame) -> Optional[np.ndarray]:
        """Extract features for AI model"""
        if len(data) < 20:
            return None
        
        # Use recent price data normalized
        recent_data = data.tail(20)
        current_price = recent_data['close'].iloc[-1]
        
        # Normalize OHLC data
        features = []
        for col in ['open', 'high', 'low', 'close']:
            normalized = recent_data[col].values / current_price
            features.extend(normalized)
        
        # Ensure fixed feature size
        feature_array = np.array(features[:20])  # Take first 20 features
        if len(feature_array) < 20:
            feature_array = np.pad(feature_array, (0, 20 - len(feature_array)), 'constant')
        
        self.feature_cache = feature_array
        return feature_array.astype(np.float32)
    
    def _load_ai_model(self):
        """Load pre-trained AI model if available"""
        if self.ai_agent:
            model_path = "models/dqn_gold_model.pth"
            self.ai_agent.load_model(model_path)
    
    def _create_empty_market_state(self) -> MarketState:
        """Create empty market state for error cases"""
        from ..indicators.technical_indicators import MACDSignal, ATRData, PivotPoints
        
        return MarketState(
            price=0, bid=0, ask=0, spread=0,
            macd_signal=MACDSignal(0, 0, 0, 'neutral', 'none'),
            atr_data=ATRData(0, 0, 'medium'),
            pivot_points=PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot'),
            ai_prediction=None, ai_confidence=0,
            market_trend='sideways', volatility='medium'
        )
    
    def get_strategy_status(self) -> Dict:
        """Get current strategy status"""
        return {
            'last_signal': {
                'action': self.last_signal.action if self.last_signal else 'none',
                'strength': self.last_signal.strength if self.last_signal else 0,
                'timestamp': self.last_signal.timestamp.isoformat() if self.last_signal else None
            } if self.last_signal else None,
            'ai_enabled': self.config.enable_ai,
            'ai_model_loaded': self.ai_agent is not None,
            'min_signal_strength': self.min_signal_strength,
            'lookback_period': self.lookback_period
        }
