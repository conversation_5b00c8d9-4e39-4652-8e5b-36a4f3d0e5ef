"""
Deep Q-Network (DQN) Agent for Gold Trading
Implements reinforcement learning for trading decision making
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque, namedtuple
import random
from typing import List, Tuple, Optional, Dict
import pickle
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.config import AIConfig

logger = get_logger(__name__)

# Experience tuple for replay memory
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])


class DQNNetwork(nn.Module):
    """Deep Q-Network architecture"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 128):
        super(DQNNetwork, self).__init__()
        
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size)
        self.fc4 = nn.Linear(hidden_size, action_size)
        
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        return x


class ReplayMemory:
    """Experience replay memory for DQN"""
    
    def __init__(self, capacity: int):
        self.memory = deque(maxlen=capacity)
    
    def push(self, experience: Experience):
        """Add experience to memory"""
        self.memory.append(experience)
    
    def sample(self, batch_size: int) -> List[Experience]:
        """Sample random batch from memory"""
        return random.sample(self.memory, batch_size)
    
    def __len__(self):
        return len(self.memory)


class DQNAgent:
    """Deep Q-Network Agent for trading decisions"""
    
    def __init__(self, state_size: int, action_size: int, config: AIConfig):
        self.state_size = state_size
        self.action_size = action_size
        self.config = config
        
        # Neural networks
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.q_network = DQNNetwork(state_size, action_size).to(self.device)
        self.target_network = DQNNetwork(state_size, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=config.learning_rate)
        
        # Replay memory
        self.memory = ReplayMemory(config.memory_size)
        
        # Exploration parameters
        self.epsilon = config.epsilon_start
        self.epsilon_end = config.epsilon_end
        self.epsilon_decay = config.epsilon_decay
        
        # Training parameters
        self.batch_size = config.batch_size
        self.target_update_frequency = config.target_update_frequency
        self.training_frequency = config.training_frequency
        
        # Counters
        self.step_count = 0
        self.episode_count = 0
        
        # Performance tracking
        self.episode_rewards = []
        self.losses = []
        
        logger.info(f"DQN Agent initialized - State size: {state_size}, Action size: {action_size}")
    
    def get_action(self, state: np.ndarray, training: bool = True) -> int:
        """
        Get action using epsilon-greedy policy
        
        Args:
            state: Current state observation
            training: Whether in training mode
        
        Returns:
            Action index
        """
        if training and random.random() < self.epsilon:
            # Random action (exploration)
            return random.randrange(self.action_size)
        
        # Greedy action (exploitation)
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return q_values.argmax().item()
    
    def store_experience(self, state: np.ndarray, action: int, reward: float, 
                        next_state: np.ndarray, done: bool):
        """Store experience in replay memory"""
        experience = Experience(state, action, reward, next_state, done)
        self.memory.push(experience)
    
    def train(self) -> Optional[float]:
        """
        Train the DQN network
        
        Returns:
            Training loss if training occurred, None otherwise
        """
        if len(self.memory) < self.batch_size:
            return None
        
        # Sample batch from memory
        experiences = self.memory.sample(self.batch_size)
        
        # Convert to tensors
        states = torch.FloatTensor([e.state for e in experiences]).to(self.device)
        actions = torch.LongTensor([e.action for e in experiences]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in experiences]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in experiences]).to(self.device)
        dones = torch.BoolTensor([e.done for e in experiences]).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values from target network
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (0.99 * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Update target network
        if self.step_count % self.target_update_frequency == 0:
            self.update_target_network()
        
        # Decay epsilon
        if self.epsilon > self.epsilon_end:
            self.epsilon *= self.epsilon_decay
        
        self.step_count += 1
        loss_value = loss.item()
        self.losses.append(loss_value)
        
        return loss_value
    
    def update_target_network(self):
        """Update target network with current network weights"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def save_model(self, filepath: str):
        """Save model and training state"""
        save_dict = {
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'step_count': self.step_count,
            'episode_count': self.episode_count,
            'episode_rewards': self.episode_rewards,
            'losses': self.losses,
            'config': self.config
        }
        
        torch.save(save_dict, filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load model and training state"""
        if not Path(filepath).exists():
            logger.warning(f"Model file {filepath} not found")
            return
        
        save_dict = torch.load(filepath, map_location=self.device)
        
        self.q_network.load_state_dict(save_dict['q_network_state_dict'])
        self.target_network.load_state_dict(save_dict['target_network_state_dict'])
        self.optimizer.load_state_dict(save_dict['optimizer_state_dict'])
        self.epsilon = save_dict.get('epsilon', self.epsilon)
        self.step_count = save_dict.get('step_count', 0)
        self.episode_count = save_dict.get('episode_count', 0)
        self.episode_rewards = save_dict.get('episode_rewards', [])
        self.losses = save_dict.get('losses', [])
        
        logger.info(f"Model loaded from {filepath}")
    
    def get_q_values(self, state: np.ndarray) -> np.ndarray:
        """Get Q-values for all actions given a state"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        return q_values.cpu().numpy().flatten()
    
    def get_training_stats(self) -> Dict:
        """Get training statistics"""
        recent_rewards = self.episode_rewards[-100:] if self.episode_rewards else [0]
        recent_losses = self.losses[-100:] if self.losses else [0]
        
        return {
            'episode_count': self.episode_count,
            'step_count': self.step_count,
            'epsilon': self.epsilon,
            'avg_reward_100': np.mean(recent_rewards),
            'avg_loss_100': np.mean(recent_losses),
            'memory_size': len(self.memory),
            'total_episodes': len(self.episode_rewards)
        }
    
    def reset_episode(self, episode_reward: float):
        """Reset for new episode and record reward"""
        self.episode_rewards.append(episode_reward)
        self.episode_count += 1
        
        # Log progress every 100 episodes
        if self.episode_count % 100 == 0:
            stats = self.get_training_stats()
            logger.info(f"Episode {self.episode_count}: "
                       f"Avg Reward: {stats['avg_reward_100']:.2f}, "
                       f"Epsilon: {stats['epsilon']:.3f}, "
                       f"Memory: {stats['memory_size']}")


class TradingEnvironment:
    """Trading environment for DQN training"""
    
    def __init__(self, data: pd.DataFrame, initial_balance: float = 10000):
        self.data = data
        self.initial_balance = initial_balance
        self.reset()
        
        # Action space: 0=Hold, 1=Buy, 2=Sell
        self.action_space = 3
        
        # State space: OHLC + indicators
        self.state_size = 20  # Will be determined by feature engineering
        
    def reset(self) -> np.ndarray:
        """Reset environment to initial state"""
        self.current_step = 50  # Start after enough data for indicators
        self.balance = self.initial_balance
        self.position = 0  # 0=no position, 1=long, -1=short
        self.entry_price = 0
        self.total_reward = 0
        self.trades = []
        
        return self._get_state()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        Execute action and return next state, reward, done, info
        
        Args:
            action: Action to take (0=Hold, 1=Buy, 2=Sell)
        
        Returns:
            Tuple of (next_state, reward, done, info)
        """
        current_price = self.data.iloc[self.current_step]['close']
        reward = 0
        
        # Execute action
        if action == 1 and self.position == 0:  # Buy
            self.position = 1
            self.entry_price = current_price
        elif action == 2 and self.position == 0:  # Sell
            self.position = -1
            self.entry_price = current_price
        elif action == 0 and self.position != 0:  # Close position
            if self.position == 1:  # Close long
                reward = (current_price - self.entry_price) / self.entry_price
            else:  # Close short
                reward = (self.entry_price - current_price) / self.entry_price
            
            self.trades.append({
                'entry_price': self.entry_price,
                'exit_price': current_price,
                'position': self.position,
                'reward': reward
            })
            
            self.position = 0
            self.entry_price = 0
        
        # Calculate unrealized P&L for open positions
        if self.position != 0:
            if self.position == 1:  # Long position
                unrealized_pnl = (current_price - self.entry_price) / self.entry_price
            else:  # Short position
                unrealized_pnl = (self.entry_price - current_price) / self.entry_price
            reward += unrealized_pnl * 0.1  # Small reward for unrealized gains
        
        self.total_reward += reward
        self.current_step += 1
        
        # Check if episode is done
        done = self.current_step >= len(self.data) - 1
        
        next_state = self._get_state() if not done else np.zeros(self.state_size)
        
        info = {
            'balance': self.balance,
            'position': self.position,
            'total_trades': len(self.trades),
            'total_reward': self.total_reward
        }
        
        return next_state, reward, done, info
    
    def _get_state(self) -> np.ndarray:
        """Get current state representation"""
        if self.current_step >= len(self.data):
            return np.zeros(self.state_size)
        
        # Get recent price data
        window = 20
        start_idx = max(0, self.current_step - window + 1)
        recent_data = self.data.iloc[start_idx:self.current_step + 1]
        
        # Normalize prices
        current_price = recent_data['close'].iloc[-1]
        normalized_prices = recent_data[['open', 'high', 'low', 'close']].values / current_price
        
        # Flatten and pad if necessary
        state = normalized_prices.flatten()
        if len(state) < self.state_size:
            state = np.pad(state, (0, self.state_size - len(state)), 'constant')
        elif len(state) > self.state_size:
            state = state[:self.state_size]
        
        return state.astype(np.float32)
