"""
Configuration management for MT5 Trading Bot
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class MT5Config:
    """MT5 connection configuration"""
    login: int
    password: str
    server: str
    symbol: str = "XAUUSD"
    magic_number: int = 234000


@dataclass
class TradingConfig:
    """Trading parameters configuration"""
    timeframe: int = 5  # 5-minute timeframe
    max_positions: int = 3
    max_daily_trades: int = 10
    risk_per_trade: float = 0.02  # 2% risk per trade
    min_volume: float = 0.01
    max_volume: float = 1.0
    volume_step: float = 0.01


@dataclass
class IndicatorConfig:
    """Technical indicators configuration"""
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    atr_period: int = 14
    pivot_method: str = "standard"  # standard, fibon<PERSON>ci, camarilla


@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_drawdown: float = 0.10  # 10% maximum drawdown
    stop_loss_atr_multiplier: float = 2.0
    take_profit_atr_multiplier: float = 3.0
    trailing_stop: bool = True
    trailing_stop_distance: float = 50  # points
    max_spread: float = 30  # maximum spread in points


@dataclass
class AIConfig:
    """AI/ML model configuration"""
    model_type: str = "dqn"  # dqn, ppo, a2c
    learning_rate: float = 0.001
    batch_size: int = 32
    memory_size: int = 10000
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    target_update_frequency: int = 100
    training_frequency: int = 4


@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: str = "2023-01-01"
    end_date: str = "2024-01-01"
    initial_balance: float = 10000.0
    commission: float = 0.0  # Commission per trade
    spread: float = 20  # Average spread in points


@dataclass
class BotConfig:
    """Main bot configuration"""
    mt5: MT5Config
    trading: TradingConfig
    indicators: IndicatorConfig
    risk: RiskConfig
    ai: AIConfig
    backtest: BacktestConfig
    logging_level: str = "INFO"
    enable_ai: bool = True
    enable_backtesting: bool = False
    live_trading: bool = False


class ConfigManager:
    """Configuration manager for the trading bot"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = Path(config_path)
        self.config: Optional[BotConfig] = None
        
    def load_config(self) -> BotConfig:
        """Load configuration from YAML file"""
        try:
            if not self.config_path.exists():
                logger.warning(f"Config file {self.config_path} not found. Creating default config.")
                self.create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config_data = yaml.safe_load(file)
            
            # Parse configuration sections
            mt5_config = MT5Config(**config_data.get('mt5', {}))
            trading_config = TradingConfig(**config_data.get('trading', {}))
            indicator_config = IndicatorConfig(**config_data.get('indicators', {}))
            risk_config = RiskConfig(**config_data.get('risk', {}))
            ai_config = AIConfig(**config_data.get('ai', {}))
            backtest_config = BacktestConfig(**config_data.get('backtest', {}))
            
            self.config = BotConfig(
                mt5=mt5_config,
                trading=trading_config,
                indicators=indicator_config,
                risk=risk_config,
                ai=ai_config,
                backtest=backtest_config,
                logging_level=config_data.get('logging_level', 'INFO'),
                enable_ai=config_data.get('enable_ai', True),
                enable_backtesting=config_data.get('enable_backtesting', False),
                live_trading=config_data.get('live_trading', False)
            )
            
            logger.info(f"Configuration loaded from {self.config_path}")
            return self.config
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
    
    def save_config(self, config: BotConfig):
        """Save configuration to YAML file"""
        try:
            # Create config directory if it doesn't exist
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert dataclass to dictionary
            config_dict = {
                'mt5': asdict(config.mt5),
                'trading': asdict(config.trading),
                'indicators': asdict(config.indicators),
                'risk': asdict(config.risk),
                'ai': asdict(config.ai),
                'backtest': asdict(config.backtest),
                'logging_level': config.logging_level,
                'enable_ai': config.enable_ai,
                'enable_backtesting': config.enable_backtesting,
                'live_trading': config.live_trading
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config_dict, file, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise
    
    def create_default_config(self):
        """Create default configuration file"""
        default_config = BotConfig(
            mt5=MT5Config(
                login=0,  # User needs to set this
                password="",  # User needs to set this
                server="",  # User needs to set this
                symbol="XAUUSD",
                magic_number=234000
            ),
            trading=TradingConfig(),
            indicators=IndicatorConfig(),
            risk=RiskConfig(),
            ai=AIConfig(),
            backtest=BacktestConfig()
        )
        
        self.save_config(default_config)
        logger.info("Default configuration created")
    
    def get_config(self) -> BotConfig:
        """Get current configuration"""
        if self.config is None:
            self.config = self.load_config()
        return self.config
    
    def update_config(self, **kwargs):
        """Update configuration parameters"""
        if self.config is None:
            self.config = self.load_config()
        
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Updated config: {key} = {value}")
        
        self.save_config(self.config)
    
    def validate_config(self) -> bool:
        """Validate configuration parameters"""
        if self.config is None:
            return False
        
        # Validate MT5 configuration
        if not all([self.config.mt5.login, self.config.mt5.password, self.config.mt5.server]):
            logger.error("MT5 credentials are incomplete")
            return False
        
        # Validate trading parameters
        if self.config.trading.risk_per_trade <= 0 or self.config.trading.risk_per_trade > 0.1:
            logger.error("Risk per trade should be between 0 and 0.1 (10%)")
            return False
        
        # Validate risk parameters
        if self.config.risk.max_drawdown <= 0 or self.config.risk.max_drawdown > 0.5:
            logger.error("Max drawdown should be between 0 and 0.5 (50%)")
            return False
        
        logger.info("Configuration validation passed")
        return True


def load_config(config_path: str = "config/config.yaml") -> BotConfig:
    """Convenience function to load configuration"""
    config_manager = ConfigManager(config_path)
    return config_manager.load_config()


def get_env_config() -> Dict[str, Any]:
    """Get configuration from environment variables"""
    return {
        'mt5': {
            'login': int(os.getenv('MT5_LOGIN', 0)),
            'password': os.getenv('MT5_PASSWORD', ''),
            'server': os.getenv('MT5_SERVER', ''),
        },
        'logging_level': os.getenv('LOG_LEVEL', 'INFO'),
        'live_trading': os.getenv('LIVE_TRADING', 'false').lower() == 'true'
    }
