"""
Logging configuration for MT5 Trading Bot
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


def setup_logger(name: str = "mt5_bot", level: str = "INFO") -> logging.Logger:
    """
    Setup main logger for the application
    
    Args:
        name: Logger name
        level: Logging level
    
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # File handler for detailed logs
    log_file = log_dir / f"mt5_bot_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler for important messages
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(simple_formatter)
    
    # Error file handler
    error_file = log_dir / f"mt5_bot_errors_{datetime.now().strftime('%Y%m%d')}.log"
    error_handler = logging.FileHandler(error_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get logger instance for a specific module
    
    Args:
        name: Module name
    
    Returns:
        Logger instance
    """
    return logging.getLogger(f"mt5_bot.{name}")


class TradingLogger:
    """Specialized logger for trading operations"""
    
    def __init__(self, name: str = "trading"):
        self.logger = get_logger(name)
        self.setup_trading_log()
    
    def setup_trading_log(self):
        """Setup specialized trading log file"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Trading operations log
        trading_file = log_dir / f"trading_{datetime.now().strftime('%Y%m%d')}.log"
        trading_handler = logging.FileHandler(trading_file, encoding='utf-8')
        trading_handler.setLevel(logging.INFO)
        
        trading_formatter = logging.Formatter(
            '%(asctime)s - TRADE - %(message)s'
        )
        trading_handler.setFormatter(trading_formatter)
        
        # Add handler if not already present
        if not any(isinstance(h, logging.FileHandler) and 'trading_' in h.baseFilename 
                  for h in self.logger.handlers):
            self.logger.addHandler(trading_handler)
    
    def log_trade_entry(self, symbol: str, action: str, volume: float, 
                       price: float, sl: float = 0, tp: float = 0, **kwargs):
        """Log trade entry"""
        message = (f"ENTRY - {action.upper()} {volume} {symbol} @ {price:.5f} "
                  f"SL: {sl:.5f} TP: {tp:.5f}")
        
        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"
        
        self.logger.info(message)
    
    def log_trade_exit(self, symbol: str, action: str, volume: float, 
                      entry_price: float, exit_price: float, profit: float, **kwargs):
        """Log trade exit"""
        message = (f"EXIT - {action.upper()} {volume} {symbol} "
                  f"Entry: {entry_price:.5f} Exit: {exit_price:.5f} "
                  f"P&L: {profit:.2f}")
        
        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"
        
        self.logger.info(message)
    
    def log_signal(self, signal_type: str, strength: float, indicators: dict, **kwargs):
        """Log trading signal"""
        message = f"SIGNAL - {signal_type.upper()} (Strength: {strength:.2f})"
        
        # Add indicator values
        if indicators:
            ind_str = " | ".join([f"{k}: {v}" for k, v in indicators.items()])
            message += f" | {ind_str}"
        
        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"
        
        self.logger.info(message)
    
    def log_error(self, operation: str, error: str, **kwargs):
        """Log trading error"""
        message = f"ERROR - {operation}: {error}"
        
        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"
        
        self.logger.error(message)
    
    def log_performance(self, period: str, metrics: dict):
        """Log performance metrics"""
        message = f"PERFORMANCE - {period}: "
        metrics_str = " | ".join([f"{k}: {v}" for k, v in metrics.items()])
        message += metrics_str
        
        self.logger.info(message)
