"""
MT5 Client - Core MetaTrader 5 Integration
Handles connection, authentication, and basic trading operations
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
import logging
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TradeRequest:
    """Trade request data structure"""
    action: int
    symbol: str
    volume: float
    type: int
    price: float = 0.0
    sl: float = 0.0
    tp: float = 0.0
    deviation: int = 20
    magic: int = 234000
    comment: str = "MT5 Gold Bot"
    type_time: int = mt5.ORDER_TIME_GTC
    type_filling: int = mt5.ORDER_FILLING_IOC


@dataclass
class PositionInfo:
    """Position information data structure"""
    ticket: int
    symbol: str
    type: int
    volume: float
    price_open: float
    price_current: float
    profit: float
    swap: float
    commission: float
    time: datetime


class MT5Client:
    """MetaTrader 5 client for trading operations"""
    
    def __init__(self, login: int, password: str, server: str):
        self.login = login
        self.password = password
        self.server = server
        self.connected = False
        self.symbol = "XAUUSD"  # Gold symbol
        self.magic_number = 234000
        
    def connect(self) -> bool:
        """Connect to MT5 terminal"""
        try:
            if not mt5.initialize():
                logger.error(f"MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Login to account
            if not mt5.login(self.login, password=self.password, server=self.server):
                logger.error(f"MT5 login failed: {mt5.last_error()}")
                mt5.shutdown()
                return False
            
            self.connected = True
            logger.info(f"Connected to MT5 - Account: {self.login}, Server: {self.server}")
            
            # Check if symbol is available
            if not self._check_symbol():
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Connection error: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5 terminal"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MT5")
    
    def _check_symbol(self) -> bool:
        """Check if trading symbol is available"""
        symbol_info = mt5.symbol_info(self.symbol)
        if symbol_info is None:
            logger.error(f"Symbol {self.symbol} not found")
            return False
        
        if not symbol_info.visible:
            if not mt5.symbol_select(self.symbol, True):
                logger.error(f"Failed to select symbol {self.symbol}")
                return False
        
        logger.info(f"Symbol {self.symbol} is available for trading")
        return True
    
    def get_account_info(self) -> Optional[Dict]:
        """Get account information"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
        
        account_info = mt5.account_info()
        if account_info is None:
            logger.error("Failed to get account info")
            return None
        
        return {
            'login': account_info.login,
            'balance': account_info.balance,
            'equity': account_info.equity,
            'margin': account_info.margin,
            'free_margin': account_info.margin_free,
            'margin_level': account_info.margin_level,
            'currency': account_info.currency,
            'server': account_info.server,
            'leverage': account_info.leverage
        }
    
    def get_symbol_info(self) -> Optional[Dict]:
        """Get symbol information"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
        
        symbol_info = mt5.symbol_info(self.symbol)
        if symbol_info is None:
            logger.error(f"Failed to get symbol info for {self.symbol}")
            return None
        
        return {
            'symbol': symbol_info.name,
            'bid': symbol_info.bid,
            'ask': symbol_info.ask,
            'spread': symbol_info.spread,
            'digits': symbol_info.digits,
            'point': symbol_info.point,
            'volume_min': symbol_info.volume_min,
            'volume_max': symbol_info.volume_max,
            'volume_step': symbol_info.volume_step,
            'contract_size': symbol_info.trade_contract_size
        }
    
    def get_current_price(self) -> Optional[Tuple[float, float]]:
        """Get current bid and ask prices"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
        
        tick = mt5.symbol_info_tick(self.symbol)
        if tick is None:
            logger.error(f"Failed to get tick for {self.symbol}")
            return None
        
        return tick.bid, tick.ask
    
    def get_historical_data(self, timeframe: int, count: int = 1000) -> Optional[pd.DataFrame]:
        """Get historical price data"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
        
        rates = mt5.copy_rates_from_pos(self.symbol, timeframe, 0, count)
        if rates is None:
            logger.error(f"Failed to get historical data for {self.symbol}")
            return None
        
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        df.set_index('time', inplace=True)
        
        return df
    
    def send_order(self, trade_request: TradeRequest) -> Optional[Dict]:
        """Send trading order"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return None
        
        request = {
            "action": trade_request.action,
            "symbol": trade_request.symbol,
            "volume": trade_request.volume,
            "type": trade_request.type,
            "price": trade_request.price,
            "sl": trade_request.sl,
            "tp": trade_request.tp,
            "deviation": trade_request.deviation,
            "magic": trade_request.magic,
            "comment": trade_request.comment,
            "type_time": trade_request.type_time,
            "type_filling": trade_request.type_filling,
        }
        
        result = mt5.order_send(request)
        if result is None:
            logger.error("Order send failed")
            return None
        
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            logger.error(f"Order failed: {result.retcode} - {result.comment}")
            return None
        
        logger.info(f"Order successful: {result.order}")
        return {
            'retcode': result.retcode,
            'deal': result.deal,
            'order': result.order,
            'volume': result.volume,
            'price': result.price,
            'comment': result.comment
        }
    
    def buy_market(self, volume: float, sl: float = 0.0, tp: float = 0.0) -> Optional[Dict]:
        """Place market buy order"""
        bid, ask = self.get_current_price()
        if bid is None or ask is None:
            return None
        
        trade_request = TradeRequest(
            action=mt5.TRADE_ACTION_DEAL,
            symbol=self.symbol,
            volume=volume,
            type=mt5.ORDER_TYPE_BUY,
            price=ask,
            sl=sl,
            tp=tp,
            magic=self.magic_number
        )
        
        return self.send_order(trade_request)
    
    def sell_market(self, volume: float, sl: float = 0.0, tp: float = 0.0) -> Optional[Dict]:
        """Place market sell order"""
        bid, ask = self.get_current_price()
        if bid is None or ask is None:
            return None
        
        trade_request = TradeRequest(
            action=mt5.TRADE_ACTION_DEAL,
            symbol=self.symbol,
            volume=volume,
            type=mt5.ORDER_TYPE_SELL,
            price=bid,
            sl=sl,
            tp=tp,
            magic=self.magic_number
        )
        
        return self.send_order(trade_request)
    
    def get_positions(self) -> List[PositionInfo]:
        """Get current positions"""
        if not self.connected:
            logger.error("Not connected to MT5")
            return []
        
        positions = mt5.positions_get(symbol=self.symbol)
        if positions is None:
            return []
        
        position_list = []
        for pos in positions:
            position_info = PositionInfo(
                ticket=pos.ticket,
                symbol=pos.symbol,
                type=pos.type,
                volume=pos.volume,
                price_open=pos.price_open,
                price_current=pos.price_current,
                profit=pos.profit,
                swap=pos.swap,
                commission=pos.commission,
                time=datetime.fromtimestamp(pos.time)
            )
            position_list.append(position_info)
        
        return position_list
    
    def close_position(self, ticket: int) -> Optional[Dict]:
        """Close position by ticket"""
        positions = mt5.positions_get(ticket=ticket)
        if not positions:
            logger.error(f"Position {ticket} not found")
            return None
        
        position = positions[0]
        
        # Determine opposite order type
        if position.type == mt5.ORDER_TYPE_BUY:
            order_type = mt5.ORDER_TYPE_SELL
            price = mt5.symbol_info_tick(position.symbol).bid
        else:
            order_type = mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(position.symbol).ask
        
        trade_request = TradeRequest(
            action=mt5.TRADE_ACTION_DEAL,
            symbol=position.symbol,
            volume=position.volume,
            type=order_type,
            price=price,
            magic=self.magic_number,
            comment=f"Close position {ticket}"
        )
        
        return self.send_order(trade_request)
    
    def close_all_positions(self) -> List[Dict]:
        """Close all positions for the symbol"""
        positions = self.get_positions()
        results = []
        
        for position in positions:
            result = self.close_position(position.ticket)
            if result:
                results.append(result)
        
        return results
