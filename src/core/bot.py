"""
Main Trading Bot Class
Orchestrates all components for automated gold trading
"""

import asyncio
import signal
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import MetaTrader5 as mt5

from .mt5_client import MT5Client, PositionInfo
from ..strategy.gold_strategy import GoldTradingStrategy, TradingSignal, MarketState
from ..risk.risk_manager import RiskManager
from ..utils.logger import setup_logger, TradingLogger
from ..utils.config import BotConfig, load_config

logger = setup_logger()
trading_logger = TradingLogger("main_bot")


class TradingBot:
    """Main Trading Bot Class"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.running = False
        self.shutdown_requested = False
        
        # Initialize components
        self.mt5_client = MT5Client(
            config.mt5.login,
            config.mt5.password,
            config.mt5.server
        )
        
        self.strategy = GoldTradingStrategy(config, self.mt5_client)
        
        # Performance tracking
        self.start_time = None
        self.total_trades = 0
        self.successful_trades = 0
        self.total_profit = 0.0
        
        # State tracking
        self.last_analysis_time = None
        self.analysis_interval = timedelta(minutes=1)  # Analyze every minute
        
        logger.info("Trading Bot initialized")
    
    async def start(self):
        """Start the trading bot"""
        logger.info("Starting MT5 Gold Trading Bot...")
        
        # Connect to MT5
        if not self.mt5_client.connect():
            logger.error("Failed to connect to MT5")
            return
        
        # Validate configuration
        if not self._validate_setup():
            logger.error("Setup validation failed")
            return
        
        # Log initial account info
        account_info = self.mt5_client.get_account_info()
        if account_info:
            logger.info(f"Account Balance: ${account_info['balance']:.2f}")
            logger.info(f"Account Equity: ${account_info['equity']:.2f}")
            logger.info(f"Leverage: 1:{account_info['leverage']}")
        
        self.running = True
        self.start_time = datetime.now()
        
        try:
            # Main trading loop
            await self._main_loop()
        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
        finally:
            await self._shutdown()
    
    async def _main_loop(self):
        """Main trading loop"""
        logger.info("Entering main trading loop...")
        
        while self.running and not self.shutdown_requested:
            try:
                # Check if it's time for analysis
                current_time = datetime.now()
                if (self.last_analysis_time is None or 
                    current_time - self.last_analysis_time >= self.analysis_interval):
                    
                    await self._trading_cycle()
                    self.last_analysis_time = current_time
                
                # Sleep for a short interval
                await asyncio.sleep(10)  # 10 seconds between checks
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                await asyncio.sleep(30)  # Wait longer on error
    
    async def _trading_cycle(self):
        """Execute one complete trading cycle"""
        try:
            # Get current market state
            market_state = self.strategy.analyze_market()
            
            # Get current positions and account info
            current_positions = self.mt5_client.get_positions()
            account_info = self.mt5_client.get_account_info()
            
            if not account_info:
                logger.error("Failed to get account info")
                return
            
            # Log current status
            self._log_status(market_state, current_positions, account_info)
            
            # Generate trading signal
            signal = self.strategy.generate_signal(
                market_state, current_positions, account_info
            )
            
            if signal:
                await self._execute_signal(signal, current_positions)
            
            # Check for position management
            await self._manage_positions(current_positions, market_state)
            
        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
    
    async def _execute_signal(self, signal: TradingSignal, 
                            current_positions: List[PositionInfo]):
        """Execute trading signal"""
        try:
            if not self.config.live_trading:
                logger.info(f"DEMO MODE - Would execute: {signal.action.upper()} "
                          f"{signal.volume} @ {signal.entry_price:.5f} "
                          f"(Strength: {signal.strength:.2f})")
                return
            
            if signal.action == 'buy':
                result = self.mt5_client.buy_market(
                    volume=signal.volume,
                    sl=signal.stop_loss,
                    tp=signal.take_profit
                )
                
                if result:
                    self.total_trades += 1
                    trading_logger.log_trade_entry(
                        self.config.mt5.symbol,
                        'BUY',
                        signal.volume,
                        signal.entry_price,
                        signal.stop_loss,
                        signal.take_profit,
                        strength=signal.strength,
                        reasoning=signal.reasoning
                    )
                    logger.info(f"BUY order executed: {result}")
                else:
                    logger.error("Failed to execute BUY order")
            
            elif signal.action == 'sell':
                result = self.mt5_client.sell_market(
                    volume=signal.volume,
                    sl=signal.stop_loss,
                    tp=signal.take_profit
                )
                
                if result:
                    self.total_trades += 1
                    trading_logger.log_trade_entry(
                        self.config.mt5.symbol,
                        'SELL',
                        signal.volume,
                        signal.entry_price,
                        signal.stop_loss,
                        signal.take_profit,
                        strength=signal.strength,
                        reasoning=signal.reasoning
                    )
                    logger.info(f"SELL order executed: {result}")
                else:
                    logger.error("Failed to execute SELL order")
            
            elif signal.action == 'close':
                # Close specific positions based on signal reasoning
                for position in current_positions:
                    if str(position.ticket) in signal.reasoning:
                        result = self.mt5_client.close_position(position.ticket)
                        if result:
                            profit = position.profit
                            self.total_profit += profit
                            if profit > 0:
                                self.successful_trades += 1
                            
                            trading_logger.log_trade_exit(
                                position.symbol,
                                'CLOSE',
                                position.volume,
                                position.price_open,
                                position.price_current,
                                profit,
                                reasoning=signal.reasoning
                            )
                            logger.info(f"Position {position.ticket} closed: P&L ${profit:.2f}")
                        break
        
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            trading_logger.log_error("signal_execution", str(e))
    
    async def _manage_positions(self, positions: List[PositionInfo], 
                              market_state: MarketState):
        """Manage existing positions"""
        for position in positions:
            try:
                # Check if position should be closed based on risk management
                should_close, reason = self.strategy.risk_manager.should_close_position(
                    position, market_state.price, market_state.atr_data.atr
                )
                
                if should_close and self.config.live_trading:
                    result = self.mt5_client.close_position(position.ticket)
                    if result:
                        profit = position.profit
                        self.total_profit += profit
                        if profit > 0:
                            self.successful_trades += 1
                        
                        trading_logger.log_trade_exit(
                            position.symbol,
                            'RISK_CLOSE',
                            position.volume,
                            position.price_open,
                            position.price_current,
                            profit,
                            reasoning=reason
                        )
                        logger.info(f"Risk management closed position {position.ticket}: {reason}")
                
            except Exception as e:
                logger.error(f"Error managing position {position.ticket}: {e}")
    
    def _log_status(self, market_state: MarketState, 
                   positions: List[PositionInfo], account_info: Dict):
        """Log current bot status"""
        # Log every 10 minutes to avoid spam
        if (self.last_analysis_time and 
            datetime.now() - self.last_analysis_time < timedelta(minutes=10)):
            return
        
        logger.info(f"=== Trading Bot Status ===")
        logger.info(f"Price: {market_state.price:.5f} | Spread: {market_state.spread:.1f}")
        logger.info(f"MACD: {market_state.macd_signal.trend} | "
                   f"Pivot: {market_state.pivot_points.current_level} | "
                   f"Volatility: {market_state.volatility}")
        
        if self.config.enable_ai and market_state.ai_prediction is not None:
            actions = ['Hold', 'Buy', 'Sell']
            logger.info(f"AI: {actions[market_state.ai_prediction]} "
                       f"(Confidence: {market_state.ai_confidence:.2f})")
        
        logger.info(f"Balance: ${account_info['balance']:.2f} | "
                   f"Equity: ${account_info['equity']:.2f} | "
                   f"Positions: {len(positions)}")
        
        if positions:
            total_profit = sum(pos.profit for pos in positions)
            logger.info(f"Open P&L: ${total_profit:.2f}")
        
        # Performance summary
        if self.total_trades > 0:
            win_rate = (self.successful_trades / self.total_trades) * 100
            logger.info(f"Performance: {self.total_trades} trades, "
                       f"{win_rate:.1f}% win rate, "
                       f"${self.total_profit:.2f} total P&L")
    
    def _validate_setup(self) -> bool:
        """Validate bot setup"""
        # Check MT5 connection
        account_info = self.mt5_client.get_account_info()
        if not account_info:
            logger.error("Cannot get account information")
            return False
        
        # Check symbol availability
        symbol_info = self.mt5_client.get_symbol_info()
        if not symbol_info:
            logger.error(f"Symbol {self.config.mt5.symbol} not available")
            return False
        
        # Check if market is open
        bid, ask = self.mt5_client.get_current_price()
        if bid is None or ask is None:
            logger.error("Cannot get current prices - market may be closed")
            return False
        
        # Validate configuration
        if not self.config.live_trading:
            logger.warning("Running in DEMO mode - no real trades will be executed")
        
        logger.info("Setup validation passed")
        return True
    
    async def _shutdown(self):
        """Shutdown the bot gracefully"""
        logger.info("Shutting down trading bot...")
        
        self.running = False
        
        # Close all positions if configured to do so
        if self.config.live_trading and hasattr(self.config, 'close_on_shutdown'):
            if self.config.close_on_shutdown:
                positions = self.mt5_client.get_positions()
                if positions:
                    logger.info(f"Closing {len(positions)} open positions...")
                    results = self.mt5_client.close_all_positions()
                    logger.info(f"Closed {len(results)} positions")
        
        # Disconnect from MT5
        self.mt5_client.disconnect()
        
        # Log final performance
        if self.start_time:
            runtime = datetime.now() - self.start_time
            logger.info(f"Bot ran for {runtime}")
            logger.info(f"Total trades: {self.total_trades}")
            if self.total_trades > 0:
                win_rate = (self.successful_trades / self.total_trades) * 100
                logger.info(f"Win rate: {win_rate:.1f}%")
                logger.info(f"Total P&L: ${self.total_profit:.2f}")
        
        logger.info("Trading bot shutdown complete")
    
    def request_shutdown(self):
        """Request graceful shutdown"""
        self.shutdown_requested = True
        logger.info("Shutdown requested")
    
    def get_status(self) -> Dict:
        """Get current bot status"""
        return {
            'running': self.running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'total_trades': self.total_trades,
            'successful_trades': self.successful_trades,
            'win_rate': (self.successful_trades / self.total_trades * 100) if self.total_trades > 0 else 0,
            'total_profit': self.total_profit,
            'live_trading': self.config.live_trading,
            'ai_enabled': self.config.enable_ai,
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None
        }
