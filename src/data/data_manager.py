"""
Data Management System for MT5 Trading Bot
Handles data collection, storage, and preprocessing for historical and real-time market data
"""

import pandas as pd
import numpy as np
import sqlite3
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import MetaTrader5 as mt5
import pickle
import json
from dataclasses import dataclass, asdict
import threading
import time

from ..utils.logger import get_logger
from ..core.mt5_client import MT5Client

logger = get_logger(__name__)


@dataclass
class MarketData:
    """Market data structure"""
    timestamp: datetime
    symbol: str
    timeframe: int
    open: float
    high: float
    low: float
    close: float
    volume: int
    spread: float = 0.0
    tick_volume: int = 0


@dataclass
class TickData:
    """Tick data structure"""
    timestamp: datetime
    symbol: str
    bid: float
    ask: float
    last: float
    volume: int
    flags: int


class DatabaseManager:
    """Database manager for storing market data"""
    
    def __init__(self, db_path: str = "data/market_data.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            # Market data table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    symbol TEXT,
                    timeframe INTEGER,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    spread REAL,
                    tick_volume INTEGER,
                    UNIQUE(timestamp, symbol, timeframe)
                )
            """)
            
            # Tick data table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS tick_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    symbol TEXT,
                    bid REAL,
                    ask REAL,
                    last REAL,
                    volume INTEGER,
                    flags INTEGER,
                    UNIQUE(timestamp, symbol)
                )
            """)
            
            # Trading signals table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    symbol TEXT,
                    signal_type TEXT,
                    strength REAL,
                    price REAL,
                    indicators TEXT,
                    ai_prediction INTEGER,
                    ai_confidence REAL
                )
            """)
            
            # Performance metrics table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME,
                    total_trades INTEGER,
                    winning_trades INTEGER,
                    total_profit REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    profit_factor REAL,
                    sharpe_ratio REAL
                )
            """)
            
            conn.commit()
            logger.info(f"Database initialized at {self.db_path}")
    
    def store_market_data(self, data: List[MarketData]):
        """Store market data in database"""
        with sqlite3.connect(self.db_path) as conn:
            for item in data:
                try:
                    conn.execute("""
                        INSERT OR REPLACE INTO market_data 
                        (timestamp, symbol, timeframe, open, high, low, close, volume, spread, tick_volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item.timestamp, item.symbol, item.timeframe,
                        item.open, item.high, item.low, item.close,
                        item.volume, item.spread, item.tick_volume
                    ))
                except sqlite3.IntegrityError:
                    pass  # Data already exists
            conn.commit()
    
    def store_tick_data(self, data: List[TickData]):
        """Store tick data in database"""
        with sqlite3.connect(self.db_path) as conn:
            for item in data:
                try:
                    conn.execute("""
                        INSERT OR REPLACE INTO tick_data 
                        (timestamp, symbol, bid, ask, last, volume, flags)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item.timestamp, item.symbol, item.bid, item.ask,
                        item.last, item.volume, item.flags
                    ))
                except sqlite3.IntegrityError:
                    pass  # Data already exists
            conn.commit()
    
    def get_market_data(self, symbol: str, timeframe: int, 
                       start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Retrieve market data from database"""
        with sqlite3.connect(self.db_path) as conn:
            query = """
                SELECT timestamp, open, high, low, close, volume, spread, tick_volume
                FROM market_data 
                WHERE symbol = ? AND timeframe = ? 
                AND timestamp BETWEEN ? AND ?
                ORDER BY timestamp
            """
            df = pd.read_sql_query(query, conn, params=(symbol, timeframe, start_date, end_date))
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            return df
    
    def store_trading_signal(self, timestamp: datetime, symbol: str, 
                           signal_type: str, strength: float, price: float,
                           indicators: Dict, ai_prediction: int = None, 
                           ai_confidence: float = 0.0):
        """Store trading signal in database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO trading_signals 
                (timestamp, symbol, signal_type, strength, price, indicators, ai_prediction, ai_confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp, symbol, signal_type, strength, price,
                json.dumps(indicators), ai_prediction, ai_confidence
            ))
            conn.commit()


class DataCollector:
    """Real-time and historical data collector"""
    
    def __init__(self, mt5_client: MT5Client, db_manager: DatabaseManager):
        self.mt5_client = mt5_client
        self.db_manager = db_manager
        self.collecting = False
        self.collection_thread = None
        self.collection_interval = 60  # seconds
        
    def start_collection(self, symbol: str = "XAUUSD", timeframe: int = mt5.TIMEFRAME_M5):
        """Start real-time data collection"""
        if self.collecting:
            logger.warning("Data collection already running")
            return
        
        self.collecting = True
        self.collection_thread = threading.Thread(
            target=self._collection_loop,
            args=(symbol, timeframe),
            daemon=True
        )
        self.collection_thread.start()
        logger.info(f"Started data collection for {symbol} on {timeframe}")
    
    def stop_collection(self):
        """Stop real-time data collection"""
        self.collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        logger.info("Data collection stopped")
    
    def _collection_loop(self, symbol: str, timeframe: int):
        """Main data collection loop"""
        while self.collecting:
            try:
                # Collect market data
                self._collect_market_data(symbol, timeframe)
                
                # Collect tick data
                self._collect_tick_data(symbol)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in data collection: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _collect_market_data(self, symbol: str, timeframe: int):
        """Collect current market data"""
        try:
            # Get recent bars
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, 10)
            if rates is None:
                return
            
            market_data = []
            for rate in rates:
                data = MarketData(
                    timestamp=datetime.fromtimestamp(rate['time']),
                    symbol=symbol,
                    timeframe=timeframe,
                    open=rate['open'],
                    high=rate['high'],
                    low=rate['low'],
                    close=rate['close'],
                    volume=rate['tick_volume'],
                    spread=rate.get('spread', 0),
                    tick_volume=rate['tick_volume']
                )
                market_data.append(data)
            
            self.db_manager.store_market_data(market_data)
            
        except Exception as e:
            logger.error(f"Error collecting market data: {e}")
    
    def _collect_tick_data(self, symbol: str):
        """Collect current tick data"""
        try:
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return
            
            tick_data = TickData(
                timestamp=datetime.fromtimestamp(tick.time),
                symbol=symbol,
                bid=tick.bid,
                ask=tick.ask,
                last=tick.last,
                volume=tick.volume,
                flags=tick.flags
            )
            
            self.db_manager.store_tick_data([tick_data])
            
        except Exception as e:
            logger.error(f"Error collecting tick data: {e}")
    
    def collect_historical_data(self, symbol: str, timeframe: int, 
                              start_date: datetime, end_date: datetime) -> bool:
        """Collect historical data for a date range"""
        try:
            logger.info(f"Collecting historical data for {symbol} from {start_date} to {end_date}")
            
            # Calculate number of bars needed
            current_date = start_date
            batch_size = 10000  # MT5 limit
            
            while current_date < end_date:
                # Get data batch
                rates = mt5.copy_rates_range(symbol, timeframe, current_date, 
                                           min(current_date + timedelta(days=30), end_date))
                
                if rates is None or len(rates) == 0:
                    current_date += timedelta(days=1)
                    continue
                
                # Convert to MarketData objects
                market_data = []
                for rate in rates:
                    data = MarketData(
                        timestamp=datetime.fromtimestamp(rate['time']),
                        symbol=symbol,
                        timeframe=timeframe,
                        open=rate['open'],
                        high=rate['high'],
                        low=rate['low'],
                        close=rate['close'],
                        volume=rate['tick_volume'],
                        spread=rate.get('spread', 0),
                        tick_volume=rate['tick_volume']
                    )
                    market_data.append(data)
                
                # Store in database
                self.db_manager.store_market_data(market_data)
                
                # Update current date
                if len(rates) > 0:
                    current_date = datetime.fromtimestamp(rates[-1]['time']) + timedelta(minutes=5)
                else:
                    current_date += timedelta(days=1)
                
                logger.info(f"Collected {len(rates)} bars up to {current_date}")
            
            logger.info("Historical data collection completed")
            return True
            
        except Exception as e:
            logger.error(f"Error collecting historical data: {e}")
            return False


class DataPreprocessor:
    """Data preprocessing and feature engineering"""
    
    def __init__(self):
        self.feature_cache = {}
    
    def preprocess_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess market data for analysis"""
        if data.empty:
            return data
        
        df = data.copy()
        
        # Calculate basic features
        df['price_change'] = df['close'].pct_change()
        df['price_range'] = (df['high'] - df['low']) / df['close']
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['close']
        df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['close']
        
        # Moving averages
        for period in [5, 10, 20, 50]:
            df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # Volatility measures
        df['volatility_5'] = df['price_change'].rolling(window=5).std()
        df['volatility_20'] = df['price_change'].rolling(window=20).std()
        
        # Volume features
        df['volume_sma_10'] = df['volume'].rolling(window=10).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_10']
        
        # Price position features
        df['price_position_5'] = (df['close'] - df['close'].rolling(5).min()) / (
            df['close'].rolling(5).max() - df['close'].rolling(5).min()
        )
        df['price_position_20'] = (df['close'] - df['close'].rolling(20).min()) / (
            df['close'].rolling(20).max() - df['close'].rolling(20).min()
        )
        
        return df
    
    def create_features_for_ml(self, data: pd.DataFrame, lookback: int = 20) -> np.ndarray:
        """Create feature matrix for ML models"""
        if len(data) < lookback:
            return None
        
        # Select relevant features
        feature_columns = [
            'price_change', 'price_range', 'body_size', 'upper_shadow', 'lower_shadow',
            'volatility_5', 'volatility_20', 'volume_ratio', 'price_position_5', 'price_position_20'
        ]
        
        # Get recent data
        recent_data = data[feature_columns].tail(lookback)
        
        # Handle missing values
        recent_data = recent_data.fillna(method='ffill').fillna(0)
        
        # Normalize features
        features = recent_data.values
        features = (features - np.mean(features, axis=0)) / (np.std(features, axis=0) + 1e-8)
        
        return features.flatten()
    
    def create_labels_for_ml(self, data: pd.DataFrame, future_periods: int = 5) -> np.ndarray:
        """Create labels for ML training"""
        if len(data) < future_periods:
            return None
        
        # Calculate future returns
        future_returns = data['close'].shift(-future_periods) / data['close'] - 1
        
        # Create labels: 0=Hold, 1=Buy, 2=Sell
        labels = np.zeros(len(future_returns))
        
        # Buy signal: future return > 0.1%
        labels[future_returns > 0.001] = 1
        
        # Sell signal: future return < -0.1%
        labels[future_returns < -0.001] = 2
        
        return labels[:-future_periods]  # Remove last periods without future data


class DataManager:
    """Main data management class"""
    
    def __init__(self, mt5_client: MT5Client, db_path: str = "data/market_data.db"):
        self.mt5_client = mt5_client
        self.db_manager = DatabaseManager(db_path)
        self.collector = DataCollector(mt5_client, self.db_manager)
        self.preprocessor = DataPreprocessor()
        
        # Cache for frequently accessed data
        self.data_cache = {}
        self.cache_timeout = 300  # 5 minutes
        
    def get_market_data(self, symbol: str = "XAUUSD", timeframe: int = mt5.TIMEFRAME_M5,
                       count: int = 1000, use_cache: bool = True) -> pd.DataFrame:
        """Get market data with caching"""
        cache_key = f"{symbol}_{timeframe}_{count}"
        
        if use_cache and cache_key in self.data_cache:
            cached_data, timestamp = self.data_cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_timeout:
                return cached_data
        
        # Get data from MT5
        data = self.mt5_client.get_historical_data(timeframe, count)
        
        if data is not None and not data.empty:
            # Preprocess data
            data = self.preprocessor.preprocess_market_data(data)
            
            # Cache the data
            if use_cache:
                self.data_cache[cache_key] = (data, datetime.now())
        
        return data
    
    def get_training_data(self, symbol: str = "XAUUSD", timeframe: int = mt5.TIMEFRAME_M5,
                         start_date: datetime = None, end_date: datetime = None) -> Tuple[np.ndarray, np.ndarray]:
        """Get preprocessed training data for ML models"""
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        # Get data from database
        data = self.db_manager.get_market_data(symbol, timeframe, start_date, end_date)
        
        if data.empty:
            logger.warning("No training data available in database")
            return None, None
        
        # Preprocess data
        data = self.preprocessor.preprocess_market_data(data)
        
        # Create features and labels
        features_list = []
        labels_list = []
        
        for i in range(20, len(data) - 5):  # Leave room for lookback and future periods
            features = self.preprocessor.create_features_for_ml(data.iloc[:i+1])
            labels = self.preprocessor.create_labels_for_ml(data.iloc[:i+6])
            
            if features is not None and labels is not None and len(labels) > i:
                features_list.append(features)
                labels_list.append(labels[i])
        
        if not features_list:
            return None, None
        
        return np.array(features_list), np.array(labels_list)
    
    def start_real_time_collection(self, symbol: str = "XAUUSD"):
        """Start real-time data collection"""
        self.collector.start_collection(symbol)
    
    def stop_real_time_collection(self):
        """Stop real-time data collection"""
        self.collector.stop_collection()
    
    def collect_historical_data(self, symbol: str = "XAUUSD", 
                              days_back: int = 30) -> bool:
        """Collect historical data for specified period"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        return self.collector.collect_historical_data(
            symbol, mt5.TIMEFRAME_M5, start_date, end_date
        )
    
    def store_signal(self, signal_data: Dict):
        """Store trading signal in database"""
        self.db_manager.store_trading_signal(
            timestamp=signal_data.get('timestamp', datetime.now()),
            symbol=signal_data.get('symbol', 'XAUUSD'),
            signal_type=signal_data.get('action', 'hold'),
            strength=signal_data.get('strength', 0.0),
            price=signal_data.get('price', 0.0),
            indicators=signal_data.get('indicators', {}),
            ai_prediction=signal_data.get('ai_prediction'),
            ai_confidence=signal_data.get('ai_confidence', 0.0)
        )
    
    def get_data_summary(self) -> Dict:
        """Get summary of available data"""
        with sqlite3.connect(self.db_manager.db_path) as conn:
            # Market data summary
            market_summary = conn.execute("""
                SELECT symbol, timeframe, COUNT(*) as count, 
                       MIN(timestamp) as start_date, MAX(timestamp) as end_date
                FROM market_data 
                GROUP BY symbol, timeframe
            """).fetchall()
            
            # Tick data summary
            tick_summary = conn.execute("""
                SELECT symbol, COUNT(*) as count,
                       MIN(timestamp) as start_date, MAX(timestamp) as end_date
                FROM tick_data 
                GROUP BY symbol
            """).fetchall()
            
            # Signals summary
            signals_summary = conn.execute("""
                SELECT signal_type, COUNT(*) as count
                FROM trading_signals 
                GROUP BY signal_type
            """).fetchall()
        
        return {
            'market_data': market_summary,
            'tick_data': tick_summary,
            'signals': signals_summary
        }
