# MT5 Python Trading Bot - Gold (XAU/USD) Specialist

An advanced automated trading bot for MetaTrader 5 (MT5) specializing in gold (XAU/USD) trading using AI/ML techniques, technical indicators, and reinforcement learning.

## 🎯 Features

- **Specialized Gold Trading**: Optimized for XAU/USD pair with 5-minute timeframe focus
- **Technical Indicators**: MACD, ATR, and Pivot Points for market analysis
- **AI/ML Integration**: Reinforcement learning models for intelligent trading decisions
- **Risk Management**: Advanced position sizing, stop-loss, and take-profit mechanisms
- **Real-time Trading**: Live market data processing and automated trade execution
- **Backtesting**: Comprehensive historical testing with performance analytics
- **Monitoring**: Real-time logging and performance tracking

## 🏗️ Project Structure

```
mt5-python-trading-bot/
├── src/
│   ├── core/                   # Core MT5 integration
│   ├── indicators/             # Technical indicators (MACD, ATR, Pivot Points)
│   ├── data/                   # Data management and preprocessing
│   ├── models/                 # AI/ML models and reinforcement learning
│   ├── strategy/               # Trading strategies and signal generation
│   ├── risk/                   # Risk management system
│   ├── backtesting/            # Backtesting framework
│   └── utils/                  # Utility functions and helpers
├── config/                     # Configuration files
├── tests/                      # Unit and integration tests
├── docs/                       # Documentation
├── data/                       # Historical data storage
├── logs/                       # Log files
├── models/                     # Trained model storage
└── requirements.txt            # Python dependencies
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- MetaTrader 5 terminal installed
- MT5 account with API access enabled

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/mt5-python-trading-bot.git
cd mt5-python-trading-bot
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure your MT5 credentials:
```bash
cp config/config.example.yaml config/config.yaml
# Edit config/config.yaml with your MT5 credentials
```

4. Run the bot:
```bash
python main.py
```

## 📊 Technical Indicators

- **MACD (Moving Average Convergence Divergence)**: Trend-following momentum indicator
- **ATR (Average True Range)**: Volatility indicator for position sizing
- **Pivot Points**: Support and resistance levels for entry/exit decisions

## 🤖 AI/ML Components

- **Reinforcement Learning**: Q-learning and Deep Q-Network (DQN) for decision making
- **Feature Engineering**: Technical indicator combinations and market state representation
- **Model Training**: Continuous learning from market data and trading outcomes

## ⚠️ Risk Management

- Dynamic position sizing based on ATR
- Stop-loss and take-profit automation
- Maximum drawdown protection
- Portfolio risk monitoring

## 📈 Performance Monitoring

- Real-time P&L tracking
- Win rate and risk-reward ratio analysis
- Drawdown monitoring
- Trade execution logs

## 🧪 Testing

Run the test suite:
```bash
python -m pytest tests/
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This trading bot is for educational and research purposes. Trading involves significant risk of loss. Use at your own risk and never trade with money you cannot afford to lose.