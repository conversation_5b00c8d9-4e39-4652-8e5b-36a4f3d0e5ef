"""
Unit tests for technical indicators
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from src.indicators.technical_indicators import TechnicalIndicators, MACDSignal, ATRData, PivotPoints


class TestTechnicalIndicators:
    """Test cases for TechnicalIndicators class"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.indicators = TechnicalIndicators()
        
        # Create sample OHLC data
        dates = pd.date_range('2024-01-01', periods=100, freq='5min')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 2000.0
        price_changes = np.random.normal(0, 0.001, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        self.sample_data = pd.DataFrame({
            'time': dates,
            'open': [p * (1 + np.random.normal(0, 0.0001)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100, 1000, 100)
        })
        self.sample_data.set_index('time', inplace=True)
    
    def test_calculate_macd_valid_data(self):
        """Test MACD calculation with valid data"""
        result = self.indicators.calculate_macd(self.sample_data)
        
        assert isinstance(result, MACDSignal)
        assert isinstance(result.macd, float)
        assert isinstance(result.signal, float)
        assert isinstance(result.histogram, float)
        assert result.trend in ['bullish', 'bearish', 'neutral']
        assert result.crossover in ['bullish_cross', 'bearish_cross', 'none']
    
    def test_calculate_macd_insufficient_data(self):
        """Test MACD calculation with insufficient data"""
        small_data = self.sample_data.head(10)
        result = self.indicators.calculate_macd(small_data)
        
        assert isinstance(result, MACDSignal)
        assert result.trend == 'neutral'
        assert result.crossover == 'none'
    
    def test_calculate_macd_none_data(self):
        """Test MACD calculation with None data"""
        result = self.indicators.calculate_macd(None)
        
        assert isinstance(result, MACDSignal)
        assert result.macd == 0
        assert result.signal == 0
        assert result.histogram == 0
        assert result.trend == 'neutral'
        assert result.crossover == 'none'
    
    def test_calculate_atr_valid_data(self):
        """Test ATR calculation with valid data"""
        result = self.indicators.calculate_atr(self.sample_data)
        
        assert isinstance(result, ATRData)
        assert isinstance(result.atr, float)
        assert result.atr > 0
        assert isinstance(result.atr_percentage, float)
        assert result.atr_percentage > 0
        assert result.volatility_level in ['low', 'medium', 'high']
    
    def test_calculate_atr_insufficient_data(self):
        """Test ATR calculation with insufficient data"""
        small_data = self.sample_data.head(5)
        result = self.indicators.calculate_atr(small_data)
        
        assert isinstance(result, ATRData)
        assert result.atr == 0
        assert result.atr_percentage == 0
        assert result.volatility_level == 'medium'
    
    def test_calculate_pivot_points_valid_data(self):
        """Test Pivot Points calculation with valid data"""
        result = self.indicators.calculate_pivot_points(self.sample_data)
        
        assert isinstance(result, PivotPoints)
        assert isinstance(result.pivot, float)
        assert isinstance(result.r1, float)
        assert isinstance(result.r2, float)
        assert isinstance(result.r3, float)
        assert isinstance(result.s1, float)
        assert isinstance(result.s2, float)
        assert isinstance(result.s3, float)
        assert result.current_level in ['above_pivot', 'below_pivot', 'at_pivot']
        
        # Check logical order: R3 > R2 > R1 > Pivot > S1 > S2 > S3
        assert result.r3 > result.r2 > result.r1 > result.pivot
        assert result.pivot > result.s1 > result.s2 > result.s3
    
    def test_calculate_pivot_points_insufficient_data(self):
        """Test Pivot Points calculation with insufficient data"""
        small_data = self.sample_data.head(1)
        result = self.indicators.calculate_pivot_points(small_data)
        
        assert isinstance(result, PivotPoints)
        assert result.current_level == 'at_pivot'
    
    def test_get_combined_signal(self):
        """Test combined signal generation"""
        result = self.indicators.get_combined_signal(self.sample_data)
        
        assert isinstance(result, dict)
        assert 'macd' in result
        assert 'atr' in result
        assert 'pivot_points' in result
        assert 'signal_strength' in result
        assert 'recommendation' in result
        assert 'timestamp' in result
        
        # Check MACD section
        macd = result['macd']
        assert 'value' in macd
        assert 'signal' in macd
        assert 'histogram' in macd
        assert 'trend' in macd
        assert 'crossover' in macd
        
        # Check ATR section
        atr = result['atr']
        assert 'value' in atr
        assert 'percentage' in atr
        assert 'volatility' in atr
        
        # Check Pivot Points section
        pivot = result['pivot_points']
        assert 'pivot' in pivot
        assert 'resistance' in pivot
        assert 'support' in pivot
        assert 'current_level' in pivot
        assert len(pivot['resistance']) == 3
        assert len(pivot['support']) == 3
        
        # Check signal strength and recommendation
        assert 0 <= result['signal_strength'] <= 1
        assert result['recommendation'] in [
            'strong_buy', 'buy', 'hold', 'sell', 'strong_sell', 'wait'
        ]
    
    def test_assess_volatility(self):
        """Test volatility assessment"""
        # Test low volatility
        assert self.indicators._assess_volatility(0.3) == 'low'
        
        # Test medium volatility
        assert self.indicators._assess_volatility(1.0) == 'medium'
        
        # Test high volatility
        assert self.indicators._assess_volatility(2.0) == 'high'
    
    def test_determine_pivot_level(self):
        """Test pivot level determination"""
        pivot = 2000.0
        tolerance = 0.0001
        
        # Test above pivot
        assert self.indicators._determine_pivot_level(2000.1, pivot) == 'above_pivot'
        
        # Test below pivot
        assert self.indicators._determine_pivot_level(1999.9, pivot) == 'below_pivot'
        
        # Test at pivot
        assert self.indicators._determine_pivot_level(2000.0, pivot) == 'at_pivot'
        assert self.indicators._determine_pivot_level(2000.0 + tolerance/2, pivot) == 'at_pivot'
    
    def test_calculate_signal_strength(self):
        """Test signal strength calculation"""
        # Create test signals
        macd_bullish = MACDSignal(0.1, 0.05, 0.05, 'bullish', 'bullish_cross')
        macd_bearish = MACDSignal(-0.1, -0.05, -0.05, 'bearish', 'bearish_cross')
        
        pivot_above = PivotPoints(2000, 2010, 2020, 2030, 1990, 1980, 1970, 'above_pivot')
        pivot_below = PivotPoints(2000, 2010, 2020, 2030, 1990, 1980, 1970, 'below_pivot')
        
        # Test bullish signal
        strength_bullish = self.indicators._calculate_signal_strength(macd_bullish, pivot_above)
        assert 0 <= strength_bullish <= 1
        assert strength_bullish > 0.5  # Should be bullish
        
        # Test bearish signal
        strength_bearish = self.indicators._calculate_signal_strength(macd_bearish, pivot_below)
        assert 0 <= strength_bearish <= 1
        assert strength_bearish < 0.5  # Should be bearish
    
    @patch('src.indicators.technical_indicators.talib.MACD')
    def test_macd_calculation_error_handling(self, mock_macd):
        """Test MACD calculation error handling"""
        mock_macd.side_effect = Exception("TA-Lib error")
        
        result = self.indicators.calculate_macd(self.sample_data)
        
        assert isinstance(result, MACDSignal)
        assert result.macd == 0
        assert result.signal == 0
        assert result.histogram == 0
        assert result.trend == 'neutral'
        assert result.crossover == 'none'
    
    @patch('src.indicators.technical_indicators.talib.ATR')
    def test_atr_calculation_error_handling(self, mock_atr):
        """Test ATR calculation error handling"""
        mock_atr.side_effect = Exception("TA-Lib error")
        
        result = self.indicators.calculate_atr(self.sample_data)
        
        assert isinstance(result, ATRData)
        assert result.atr == 0
        assert result.atr_percentage == 0
        assert result.volatility_level == 'medium'


if __name__ == '__main__':
    pytest.main([__file__])
